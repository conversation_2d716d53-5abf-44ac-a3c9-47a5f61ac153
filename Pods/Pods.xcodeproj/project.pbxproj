// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 71;
	objects = {

/* Begin PBXBuildFile section */
		0231505A0491BAF427A1E649B29FDB00 /* EditorStickersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F91B5C170D89D912AD9E1CD05F85F382 /* EditorStickersView.swift */; };
		0294727FA92458294940E5A4E19AF8A8 /* PickerResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = B95858DD73C99FD8E75B920B2FA910E2 /* PickerResult.swift */; };
		047B35E033F66EC193E901673F63B1FE /* Core+UIApplication.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21E63CA427D8D83D8A8CBED8F28B0BDB /* Core+UIApplication.swift */; };
		049F02D95FC1D51F2FDC9BCC338C8426 /* EditorViewController+Action.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE803FCCDB346C8B5264CF06CA18C471 /* EditorViewController+Action.swift */; };
		******************************** /* SliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* SliderView.swift */; };
		04E0FD8AFF834F9A368C23E1D6E2FC87 /* PhotoPickerControllerFectch.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99012C6177330A6C13868B40DFF3769C /* PhotoPickerControllerFectch.swift */; };
		059FDB4AFA187A2C2F35F27BFE6C0FED /* CameraViewController+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C8FABE878D0FA2D18EAB57A981E8A0A /* CameraViewController+Preview.swift */; };
		05FE076907C9274A68280B31FB18E393 /* EditorAudioAnimationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C87B621086B1C54011333599EFF2723 /* EditorAudioAnimationView.swift */; };
		063A899587A428B29E01AB14B05D8584 /* EditorContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0ED4F6C9D4069955665F6EBF056A37A9 /* EditorContentView.swift */; };
		065BEDFDD7BA4A8F9E2975E68D092418 /* EditorFrameView+VideoPlay.swift in Sources */ = {isa = PBXBuildFile; fileRef = A61E287ACF3EC037C9C36340833BCA4D /* EditorFrameView+VideoPlay.swift */; };
		068D871E99CA49B8FE5F619592D4CAB4 /* HXTableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC3F61EF1E83C226994151C4DA44EEAA /* HXTableView.swift */; };
		069A5CCCBD3576373B5FC1DB69FAF8F4 /* PhotoEditorFilter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3F214E118F12A7332004441BAE7DF6AE /* PhotoEditorFilter.swift */; };
		069D4B78EAD1F49FD8699D423A312885 /* VideoEditorMusic.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7FF715A105B83DCE65C4A4B111DF8F1C /* VideoEditorMusic.swift */; };
		06F3282E415C4D0D4FAA687C191EE2F8 /* PhotoFetchAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD3E5E2509AEC5E6186CDF3D555F01D1 /* PhotoFetchAsset.swift */; };
		0789BB681C1946CE14BB8AE518B40C50 /* EditorMaskView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C15AFE57CE5A467ED2A4BF3A817935AA /* EditorMaskView.swift */; };
		09A56C3A3ACC12E179623043504478B1 /* PhotoPickerViewController+Camera.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D06151F50611D629C971B8BC33B3808 /* PhotoPickerViewController+Camera.swift */; };
		0A39A1868886A5F4046984C4131F349E /* PhotoPreviewSelectedView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1857AC1826A33B35FBA00100533048B /* PhotoPreviewSelectedView.swift */; };
		0A4DF949FA08A578D4A5AC8D35E236F8 /* EditorAdjusterView+ScreenRotation.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD5968928B9F0760538F06AD6D8AC691 /* EditorAdjusterView+ScreenRotation.swift */; };
		0A7C7414326F7F7AF04F12D83722C035 /* EditorControlView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90E82FF5E117D9E926606C741F0D77B3 /* EditorControlView.swift */; };
		0B70498CB7273343FA015D2D4572F186 /* PhotoImageCancelItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4FB35256EFE59BCC86317CC292956190 /* PhotoImageCancelItemView.swift */; };
		0CA058366DAF34BC9974EA3F8809A3BB /* PhotoAsset+Local.swift in Sources */ = {isa = PBXBuildFile; fileRef = E952CC3A5EC689451CFAE84702F64631 /* PhotoAsset+Local.swift */; };
		0D26A743870F898351BD096ABCB4FF9E /* PhotoPickerListFectchCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B028000BCCDD071B781E2250027AE9A8 /* PhotoPickerListFectchCell.swift */; };
		0DFFCE7A013D6C603BEACD43E79798F8 /* Core+UIBarButtonItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 684378CFCE2AD25B8D014F502640003B /* Core+UIBarButtonItem.swift */; };
		0F60986B410BEAD5BC32F0E48BB86B36 /* AssetManager+LivePhoto.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD47D725FDFC90C237F87E7786745ABA /* AssetManager+LivePhoto.swift */; };
		0FAD3AA12D9FB2EA23100492C034847A /* PhotoPreviewContentPhotoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 52B7E9208D24DE8C9A2C2749922B9D78 /* PhotoPreviewContentPhotoView.swift */; };
		10AD1FCCB522238346EB484407EA3889 /* PhotoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE2A51B48878BC9E292BF33501799144 /* PhotoManager.swift */; };
		110256DA86C253C688595F0E23F82E64 /* Core+Data.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC939ECBBB358DFEB87C5A343C18A949 /* Core+Data.swift */; };
		11EC7C878EEA96C6D2E7EC848106413B /* TickView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ADA96359B286FFEEDE25CDA2AAD9020A /* TickView.swift */; };
		12324F44E1A4D621BDFD40E1634E1E50 /* EditorCanvasView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00C43FF90F67084C11C94811FA62D7E9 /* EditorCanvasView.swift */; };
		13710A5FB4AF056762112E19815E6FA7 /* Picker+PhotoTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59192AD9A197F2CBF49D979E61BBCBCF /* Picker+PhotoTools.swift */; };
		139108194D6874EBAAB1B69EEC1897BA /* PhotoPreviewSelectedViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3B616E9E3990DEF8AD9C998E20ACD70 /* PhotoPreviewSelectedViewCell.swift */; };
		141E3415C9CC3508B8741B4DD04D3541 /* AssetManager+LivePhotoURL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22E9F2CFA47CE7855970CBDEB669FA97 /* AssetManager+LivePhotoURL.swift */; };
		16C2F1099AD708B388D0A0092AA78866 /* EditorAdjusterView+Video.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D4CE2129947066D5EE5C99EB05F726C /* EditorAdjusterView+Video.swift */; };
		1704941945F8F75868500268E2D8E6E7 /* EditorAdjusterView+ScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8AF58A82392843352FC90EF232DD7568 /* EditorAdjusterView+ScrollView.swift */; };
		1A9753C7D97E0AEC64776015F275853E /* PhotoPickerView+Editor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3EC4A7EB7CE9B8A383291613A329FCC8 /* PhotoPickerView+Editor.swift */; };
		1C50B26888B74E66F5EB5567E16D2B7C /* PhotoAsset+Equatable.swift in Sources */ = {isa = PBXBuildFile; fileRef = B41DF9BB358A82B74EE3BE34D88E27FE /* PhotoAsset+Equatable.swift */; };
		1C6459974E86F169198C12D122CEC242 /* EditorView+AdjusterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB5D5527F2F44719CC3E71843A22A15B /* EditorView+AdjusterView.swift */; };
		1CA7968C4102A3EE4F383AF962B976AF /* Core+Bundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38B19E205EADA4E98976410EDF249E9A /* Core+Bundle.swift */; };
		1CC5F19098A889DB68ACBADD7A8F5F46 /* PhotoPreviewViewController+Editor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D1A2DCD5A6F0435D9169278412A2AC0 /* PhotoPreviewViewController+Editor.swift */; };
		1D3E45E14940A615399DD9FD41D4D529 /* EditorVideoCompositor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 165848B829E7D377966C7F5A8418690C /* EditorVideoCompositor.swift */; };
		1D530B48E2747D03E4E4F4FAFCADCA11 /* EditorChartletViewListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9DCB0968DF5093644B0644294F4E27A9 /* EditorChartletViewListCell.swift */; };
		1DEF607217D3613AF9AB209DC23A6D8D /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CA0FA23181E1B0F518C7A51B4343E598 /* Foundation.framework */; };
		1E675CF386023A02DF266265C9DBFD34 /* PhotoPickerSelectableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A7666775D8F08F87AE8FB5215AC54E79 /* PhotoPickerSelectableViewCell.swift */; };
		1EA558734E0FF885FE9E316B2BE97608 /* PickerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2E28BEF6DA14C2C9EF3B674F8182DFBE /* PickerManager.swift */; };
		1F43DBCD2B0AFB949B27D20A613A22F2 /* EditorVideoControlViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1BF7C2A5D4AC66058E52AB8F224E69D /* EditorVideoControlViewCell.swift */; };
		2034AF4527E6E716F851F5DB6E3A3991 /* EditorStickerTextView+Delegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 640168DE94AFC74AA086F0F2C9D604EF /* EditorStickerTextView+Delegate.swift */; };
		2122D66069833F0A169100315B1C7CA5 /* EditorFilterParameterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE2DA113C179A1212BD24A28C3BE336F /* EditorFilterParameterView.swift */; };
		2245D941DEB1A4A701A78FB8981FEE08 /* EditorViewController+ToolsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9D1ADF73EAB464C50E9A5AFEAF8493AE /* EditorViewController+ToolsView.swift */; };
		23286D3F937F555B1FEBBDD33559A2CC /* PhotoPickerController+PickerData.swift in Sources */ = {isa = PBXBuildFile; fileRef = F4C3FB76FE244C0AC4B23E5AE81BEFA1 /* PhotoPickerController+PickerData.swift */; };
		2365DF6E1CA282F95099BAB1872D1B8D /* EditorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = F3009C554D917A450D3FBA61DD02AC8C /* EditorViewController.swift */; };
		2399E5BF2C7D8AD4ACF1AF9402532086 /* PreviewLivePhotoViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6696A56ACAAD989023AEBB20B528DD8 /* PreviewLivePhotoViewCell.swift */; };
		23A9B7FBD58B232AEE6727F456A8C572 /* EditorModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 40AB1A4DDA719AB7A53DE2AB85638BC2 /* EditorModels.swift */; };
		2550F732E25292A19EA01A8EFC2A8D79 /* EditorVolumeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E47C190F455C50D813B837ED03A6DB8F /* EditorVolumeView.swift */; };
		268846FCA0CE6314BCF55623DAAEABFA /* Picker+Int.swift in Sources */ = {isa = PBXBuildFile; fileRef = 745BDB2F10187F02CE1A4F6111908D43 /* Picker+Int.swift */; };
		2805F4E48D65F7E825E7269BCA32A162 /* CameraResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A2D446A23DD905251DD8B14D443EE9D1 /* CameraResultViewController.swift */; };
		281C1E545CF9F25E05EBE1B017AF1EF8 /* PhotoPickerControllerInteractiveAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58A6C3C54FBCE42AC4591797FDBAE095 /* PhotoPickerControllerInteractiveAnimator.swift */; };
		283636146D8930C07D55E9AB91F6DAA2 /* EditorView+UIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = A31D7F0AD3CD39B734A09530C42A6005 /* EditorView+UIImage.swift */; };
		289706A79FF0D28513256167C16EDAC1 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 662FC3274A5CFCC38963FEE272890857 /* PrivacyInfo.xcprivacy */; };
		29BC07E59105A0DBA36F3771767C60C8 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CA0FA23181E1B0F518C7A51B4343E598 /* Foundation.framework */; };
		******************************** /* PhotoPreviewViewController+Toolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* PhotoPreviewViewController+Toolbar.swift */; };
		2AB2CEBF90DB5017FE4F73EA4000BED6 /* CameraConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = B286B37B58042187E66234C7AC79EB18 /* CameraConfiguration.swift */; };
		2B1DB92F0277D2CBDB0708B7F53CC469 /* EditorAdjusterView+Rotate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 72208964807741AD183F3E36D83A90A7 /* EditorAdjusterView+Rotate.swift */; };
		2BE86EA46DB846CA4366A3B4FB542F5E /* EditorViewController+UINavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 50732B5E3676C444E0BEBA6275E6170A /* EditorViewController+UINavigationController.swift */; };
		2C4F88ECFA8CEC134CED2B2C8369D41F /* EditorVideoTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 68D33C4364FB684AFD506497AA7BB264 /* EditorVideoTool.swift */; };
		2C7EEAB731F71F6EF6A1CC15C20DFC81 /* PhotoPreviewViewController+NavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 991EB681C7EC3345C2100833B424F65C /* PhotoPreviewViewController+NavigationController.swift */; };
		2CB9277AEFB22070E81BA95350F513AE /* HXPhotoPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6ED0C3B1CB142719C5CA1CB3FE4762AE /* HXPhotoPicker.swift */; };
		2D171A6E3C98F90C81A1561BD1506046 /* PhotoPickerFinishItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E47BA054C8BFFBB6A6789DA343121110 /* PhotoPickerFinishItemView.swift */; };
		2DE1AAABFEB9C73800A9037EAEB9F38B /* PhotoAssetCollection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 37F795FE155BCD35FD53D08B7A4D0D53 /* PhotoAssetCollection.swift */; };
		2DFA94B4C6396B4323FF94B134618718 /* PhotoPickerBaseViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2DA9D39D88FA1D552DAE0658AD436F71 /* PhotoPickerBaseViewCell.swift */; };
		2EDF6C8155999174AADF520C998E5557 /* CameraNormalPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D8D93421E2B5D1BDD506143F05509416 /* CameraNormalPreviewView.swift */; };
		2FE7B8CDF43FB456370B78D9ED8AF950 /* PickerInteractiveTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 27827C41841C74D4E098A0DF83D451CF /* PickerInteractiveTransition.swift */; };
		31B86D9A156F7D44453138C1613566ED /* PhotoAsset+Image.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FB3244CF8960971D9602C2587E1BDC2 /* PhotoAsset+Image.swift */; };
		31CB4A2657B8DDB27681998914E06F15 /* TextManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE633388BB93243DEDBBC5AB30A8440F /* TextManager.swift */; };
		343163896BF7A76E77B00C67E35FE031 /* Picker+PHAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80E9543D306FF7D79E78EAF673F58FF7 /* Picker+PHAsset.swift */; };
		344761B9B00FC1DAB0C9B096945D351C /* EditorConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 841297B6767DB799B3FE634A2860CCAB /* EditorConfiguration.swift */; };
		3621FBA32F1E8D96B380CA58A9574A75 /* EditedResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9EE28813A5C6684267B19D131168519 /* EditedResult.swift */; };
		3780A663CBB7545DBCD023AA09967BFE /* EditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A35E34E546734B7D01B703E5B888E06 /* EditorView.swift */; };
		3963AC5F52AA88A234BF8294EDC17F6C /* EditorBrushBlockView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D7EF51C12D0FA4B39CF1AAFE766D3731 /* EditorBrushBlockView.swift */; };
		397F9B41BA46A7C2284772489141011D /* JDTImageModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C5D77296D1951C4C87F7F80C91549A1 /* JDTImageModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		397FF29F31D42024FDD1D5C39497B139 /* PhotoBrowserAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F12217D5E6CDEE2AECA748ABA136E6 /* PhotoBrowserAnimator.swift */; };
		398B1B8540C146FA10C407E7C5D8627F /* PhotoPreviewListViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B269ACD9FC933988CDB3751B959D0D61 /* PhotoPreviewListViewCell.swift */; };
		3A96CDCE1022A23415946DEA63D012AD /* Picker+PhotoAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 709B4104624C830A8111F35B2E3635CA /* Picker+PhotoAsset.swift */; };
		3B156DCA4941F522609A11FB571004AA /* AlbumViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E11883FCF15058AA2511443DD7B79548 /* AlbumViewCell.swift */; };
		3BD3BFC1DABB62ADF0DAF49276E90B15 /* HXLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = E084AF0B5055E5809688BD222BDEEB84 /* HXLog.swift */; };
		3C718CA5D365F69C0B0B7C802AA4A1E3 /* PhotoPreviewViewControllerProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* PhotoPreviewViewControllerProtocol.swift */; };
		3CEC6C4EB076539C4F236737F5F12DCF /* CameraController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 89C068E6FDF9FF0EE81C869EF03FC1F0 /* CameraController.swift */; };
		3DB735CB574F807AC51883F35A8D2CD2 /* EditorVideoPlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D59F5CD82CC8D5E6AEDDB7B9FA1989E /* EditorVideoPlayerView.swift */; };
		3E27495D0A1BBFB2F0807246C84E514B /* EditorTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11D34D65AFB3F2CD87764A64C97F93B7 /* EditorTransition.swift */; };
		3E977D12DEB787F907D57A658ACACD12 /* AssetManager+AVAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 418E12F5E563D168DADA19F334A7DF89 /* AssetManager+AVAsset.swift */; };
		3F4BC18C2D815B06AB9BF39D38A35014 /* PhotoPickerSwitchLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DEFE13B97AFC1F24F4FC06783E7C986 /* PhotoPickerSwitchLayout.swift */; };
		3F4D9DF8F22F09E3C07DDF9641918300 /* EditorViewController+VideoControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 417AA4B3328803AC6CD258923E2716E7 /* EditorViewController+VideoControl.swift */; };
		3FE5ECF5269F5056D39F1CF68106BA1B /* JDTImageModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 3474C413E03BAB49A675FA270CA6943F /* JDTImageModule.m */; };
		4190DD3AB88E966137A04A9CDC08248F /* PhotoPreviewViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = D54CC9078459F968A300E298851EBD83 /* PhotoPreviewViewController.swift */; };
		422AA2A05F85EDCC802432F28A97E747 /* EmptyViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0978A23FA7F89FCF52E275CA641B3941 /* EmptyViewConfiguration.swift */; };
		4366B429396FCDAB3B6599D486D7DA9A /* CaptureVideoPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 69047A871878B25CFA264C7DB91FF159 /* CaptureVideoPreviewView.swift */; };
		43B014FCC820E35F13024A718F724CCC /* PhotoPreviewContentLivePhotoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6F505333A0F849FEEECA136226F47AE /* PhotoPreviewContentLivePhotoView.swift */; };
		44CD38BC16ED32F10F4DFF5E85AD6EB1 /* PhotoThumbnailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 887E8C30595E12D37E1C57819C00A1EC /* PhotoThumbnailView.swift */; };
		45941BBF42602BCDF4A88E51642AA2AD /* ImageResource.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE10BE8BC584F1EA0A3AC092B6CED14C /* ImageResource.swift */; };
		45EB5B45F50D1F8FE50A33C01FBB01C7 /* PhotoPickerListCondition.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB747034427E6C9D3F8EDF6B0074397A /* PhotoPickerListCondition.swift */; };
		48154D2EB68512DBC7F899F938D8CCD4 /* PhotoPickerListAssets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D002CB238D49910630F3ADAFD15AFB0 /* PhotoPickerListAssets.swift */; };
		496F4BFE09CD95F1AECCAA55A361BD71 /* CameraViewController+Editor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 199A0EFAEAFB5F754B4F1DD54F46A303 /* CameraViewController+Editor.swift */; };
		4A3A86ECAAD9D8862571E4977C5D7E33 /* PhotoAsset+Editor.swift in Sources */ = {isa = PBXBuildFile; fileRef = D616C9955B8A7FD19177B2A4077686F1 /* PhotoAsset+Editor.swift */; };
		4B558B0C51EF0BB543D0716AECA07F54 /* PhotoPickerFilterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = AD35F273D9746B18C4BB6E036FBF455C /* PhotoPickerFilterViewController.swift */; };
		4B9F8EFB1C83153A069CE3C4870CFBFD /* Core+UILabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9858C8CB63795012D11DB2823D26F624 /* Core+UILabel.swift */; };
		4BB92941B96CDC7846696FD49908D987 /* PhotoPickerLimitCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56E3CB033EDC9733A7E9B4066DFCBA40 /* PhotoPickerLimitCell.swift */; };
		4BE8F60A89FAF9C69E60590694DF942D /* EditorViewController+Chartlet.swift in Sources */ = {isa = PBXBuildFile; fileRef = B34EDEE6495B2C5E198EE324134DBAED /* EditorViewController+Chartlet.swift */; };
		4D68AD3747130C9486C80692A7397B19 /* AlbumListConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8619831D4C378D268F23BD9140F6ED /* AlbumListConfiguration.swift */; };
		4D6BF38FD8738A7770DE8155BCF6761A /* HXImageViewProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 978829349A370A8F5C784CB41ACDC16D /* HXImageViewProtocol.swift */; };
		4E701B90B03D0552F2D6E43CBBCCC91F /* PhotoPeekViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BE530E5EFC06D9B34EE179F2FE0DA36 /* PhotoPeekViewController.swift */; };
		4F41F6F2A2C13625CAF61944D7E27403 /* EditorMusicViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD92419874653D9402260A514AE16E6B /* EditorMusicViewCell.swift */; };
		4FE4BC84DF17E083C5A9CC86951CD5DA /* PhotoPanGestureRecognizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 53CB4DA0AEAC9C4E53EB1A56095D9C60 /* PhotoPanGestureRecognizer.swift */; };
		52379E08BD62C7842F89C1969FDF4232 /* PhotoTools+File.swift in Sources */ = {isa = PBXBuildFile; fileRef = ACB89A1E18FFC9DA950A69DB608CE435 /* PhotoTools+File.swift */; };
		534021B56AC29C7DCB7C2BB82D97375B /* PhotoFetchAssetCollection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 561B5B856F55D2AB6B4B1CD77E1E19BE /* PhotoFetchAssetCollection.swift */; };
		536E2BDAF86FA95C151391EE23339128 /* EditorViewController+Processing.swift in Sources */ = {isa = PBXBuildFile; fileRef = 96A74C56499F32B1BA5EFD69F80379EB /* EditorViewController+Processing.swift */; };
		538FB8AEFCBA61936F28516F80E25A47 /* EditorMusicView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 597F9F432888B2C069145B01FDB8FC41 /* EditorMusicView.swift */; };
		57FD423C362F9E8EE0046CAA5567BB4A /* PhotoAlbumCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD90FAB3042955AC4E9305B33EE222FD /* PhotoAlbumCollectionCell.swift */; };
		586358E87CC311F45E2766E7DCD90F5D /* PhotoPickerControllerProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 094DC497053AE73D659B6748B85D89CF /* PhotoPickerControllerProtocol.swift */; };
		59A4446A67A63F0D4BCCAF8165A682A9 /* EmptyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A81FBE6335FD8A4A976CCD9FD672BC8 /* EmptyView.swift */; };
		59E30077498B82F5C00F7684B5F0495D /* Editor+CIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8C67DC298675C064968142DBFC591EE /* Editor+CIImage.swift */; };
		5A483113B229CB6F8730B6CF705B431F /* SwiftAssetURLResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6A24E16A6DD4F0CFFEFD938B47114A7 /* SwiftAssetURLResult.swift */; };
		5ACB3C49D7AF640ADDEC4AE1539F62E0 /* PickerConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 37B072392549BF2CAF4368B476DF02D5 /* PickerConfiguration.swift */; };
		5B91752FF79445C23F7080A4AF0B7580 /* EditorMusicLyricViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1CB2DE05E19C0060AFC9A15A5DC3624 /* EditorMusicLyricViewCell.swift */; };
		5B954DF2C1605BEC83A4823A5C8C1BDA /* EditorBrushColorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33E74DC8F2D7C01FC765E9EBD9359F60 /* EditorBrushColorView.swift */; };
		5C18B2BA1B17C1FFE6F01FD64DAB1E6A /* EditorFrameView+Control.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA3BA9322CB4F79368082AE32FD17625 /* EditorFrameView+Control.swift */; };
		5C1D538E93467350635E61D01344DBA1 /* NotAuthorizedConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FAA07E956B3AD5059A8B4053922E36A /* NotAuthorizedConfiguration.swift */; };
		5D3166A4D6E45B82FF281FF794425468 /* SwiftPhotoAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 492F778245EEAA2DD7580A4CA75F2D47 /* SwiftPhotoAsset.swift */; };
		5DE0EB87D24196E93FD9E5A7941FFC68 /* PhotoAlbumController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4EA7303E022EA3333F6C26C391E8B29F /* PhotoAlbumController.swift */; };
		5FF6A7544DC1DE8894DC82E21FD89BE8 /* PhotoPickerControllerAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2513C6E38C06FB3EA2DA85C9C481B767 /* PhotoPickerControllerAnimator.swift */; };
		601AAD3564D19F2BB8DB4FE7D6D815E5 /* EditorChartletViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A486608DC3B74B458F3C4B6E599F6F8 /* EditorChartletViewCell.swift */; };
		607C8013C18BC7E814467B3167CA197B /* EditorView+GestureRecognizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 25B65F9DDDCDD90E43BE7797FED3D5C0 /* EditorView+GestureRecognizer.swift */; };
		609C0361B7025F4562BD774B6475FC0F /* EditorMosaicToolView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23468F201EEFB6C1082900082A4B9A19 /* EditorMosaicToolView.swift */; };
		61E336411580D49811AAAC36717F621A /* PassThrough.metal in Sources */ = {isa = PBXBuildFile; fileRef = CB1CA96220C9601CD51E0FB92468894F /* PassThrough.metal */; };
		62ADB38DA0FAE0C4F68E96316AD5B63E /* PhotoPickerView+Camera.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD9390C93CE525ED5F7F409A50C6020A /* PhotoPickerView+Camera.swift */; };
		62EC8C82566861FC10AE12330E439EAC /* ExpandButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FD9805A29369B3863D67F309EDA5418 /* ExpandButton.swift */; };
		63A4E2D170F19839F4FBA790C579AFB4 /* VideoPlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F0287CE4F49D241FB9C0D4B592BE89E0 /* VideoPlayerView.swift */; };
		652CAA9CE102CD8EBFF1FFCD9B7DEAD8 /* PhotoPreviewVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8517B133E25CDB1B7DF00FA567D5D57A /* PhotoPreviewVideoView.swift */; };
		65D3359C78E19B5161412C44E5F9A388 /* PhotoPickerControllerAnimationTransitioning.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB245637C4B63307AC6BE18D3A0D612B /* PhotoPickerControllerAnimationTransitioning.swift */; };
		65F3D9527922CD3E97BFB7BA75F06186 /* Core+UIViewRTLFrame.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A3D1C145E71E4F182E42119EAD815AE /* Core+UIViewRTLFrame.swift */; };
		6654F00D2F23B297BC69C017AC92E5F6 /* CameraPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E9F53129D099F4CD9F7E057C869CA57C /* CameraPreviewView.swift */; };
		66A95237274807D361298EE5F2CFD8AC /* PhotoToolBarEmptyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 03841F568EDB504253E5A557B4B435BD /* PhotoToolBarEmptyView.swift */; };
		66B728964C6151E80727040119750ACD /* AssetManager+PlayerItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC20FD29AD5E6CDFE0C4AD2EE79DA360 /* AssetManager+PlayerItem.swift */; };
		66BF424749D8826120E9131691CF23EE /* ImageViewConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA86496717AD4C66D9FADDC12E0E8763 /* ImageViewConfig.swift */; };
		66F77F9B4A99261979F7C3FF76B7142B /* PhotoPickerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3383F8E2C5FB0994A99CB3CE18A7FFB6 /* PhotoPickerController.swift */; };
		6730B3E7B8B1B1E02E877815BB4415B5 /* PhotoTools+Camera.swift in Sources */ = {isa = PBXBuildFile; fileRef = D46473C225BD60D66F3589E97477E36B /* PhotoTools+Camera.swift */; };
		674ED2701B9D30F36070A8DD6A16771F /* NetworkAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 574A182DB58FE7697656B8FAB2DB50BB /* NetworkAsset.swift */; };
		67975D41C640F58BA1012A36D629A059 /* EditorViewController+LoadAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0346A114CDA8038E97C997FEAB11F808 /* EditorViewController+LoadAsset.swift */; };
		68167A2A22AB56DB0C252A46814626BF /* ArrowViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF237DA868C1DC44FBBCB74C835EF355 /* ArrowViewConfiguration.swift */; };
		698D6BDB7349E16A8B2809E1472DE9E5 /* EditorAdjusterView+ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 931D28714877B0595FAFF2988CAD21D1 /* EditorAdjusterView+ContentView.swift */; };
		6A32A8A5890DA2C8C5BB987C55A6032A /* EditorViewController+Music.swift in Sources */ = {isa = PBXBuildFile; fileRef = EDE2BAED3C9DA439ED017DF8480D79B0 /* EditorViewController+Music.swift */; };
		6A484BF0B4CAF05EBC4B6452302FF585 /* AssetManager+Image.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3668AB6CABB5FEC0ABCF6210CDD0E3C7 /* AssetManager+Image.swift */; };
		6A5F4AF035F97F95EF839DFCC898A9FD /* ArrowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D80F2BCBF539D3EA75EE54F08418537 /* ArrowView.swift */; };
		6A738CDB4AAB8C820F24E3BCCB944E65 /* PhotoPreviewContentViewProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7BFD29E391E22EE680A7699DA35A0B43 /* PhotoPreviewContentViewProtocol.swift */; };
		6B0729F954A4B8063805F72E60356AD7 /* PhotoPickerViewController+Toolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 87426884B220B437C2317FA73CF0B5E9 /* PhotoPickerViewController+Toolbar.swift */; };
		6BF9DD7A0234BB3A1B75E981C755591E /* AssetPermissionsUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F6CA6B226ECED60A2DBDCA59945C970 /* AssetPermissionsUtil.swift */; };
		6C2AE2B3011CEB16B3FFD317756F6C1E /* EditorMosaicView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 86EA281DBA8E46CACFBC2D77FBEEDA60 /* EditorMosaicView.swift */; };
		6C495C5E8012A77E687698406E56F134 /* EditorChartletPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6DA0B4C54C3AB43E54B7F029910B3C2 /* EditorChartletPreviewView.swift */; };
		6C69E7EAB0BE1C02B969022523213D56 /* AlbumViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0379962A2D6FAADB683611C2271421AB /* AlbumViewController.swift */; };
		6D9AD25D7865F25007CD87ED105D057B /* EditorView+UIView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9CC9CB4577D12D6D1A7DECEA072D9422 /* EditorView+UIView.swift */; };
		6DAD89AC5A6C6A43BC1AA99872684CA5 /* Picker+PHAssetCollection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8CA16080C639896A2CD61AC4F98F8343 /* Picker+PHAssetCollection.swift */; };
		6DEA6664BD227E4E53FD2AC0F3E05847 /* PreviewMetalView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 75048AB2F7832942E5A02A116732FD0C /* PreviewMetalView.swift */; };
		6E21F39B0D6DF2E878419BE08D7FB120 /* EditorChartlet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8BFB97059FCFEA3D48BA32FE9D6A616C /* EditorChartlet.swift */; };
		6EFA245A3A4DD7D19BDBA697BD1BF60B /* EditorFiltersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81913EF7492B4077BB4E51D74E6D0320 /* EditorFiltersView.swift */; };
		6EFC2064B5FE6816B2AF4BBFCDDEF510 /* PhotoAlbumHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 300430C0A4A9689485F7ECF4239E7082 /* PhotoAlbumHeaderView.swift */; };
		6FBE6900FCAB9E38E002DE9327487B5B /* EditorStickerTextView+Draw.swift in Sources */ = {isa = PBXBuildFile; fileRef = EC1A5AAA0EBE9051923DB55040755C65 /* EditorStickerTextView+Draw.swift */; };
		70A081593BBA97C77CD291D6DF4ECED7 /* ProgressImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5422CA8608B2FA688A7D7D644AB745E6 /* ProgressImageView.swift */; };
		71137B108952D67E23DDAD450AF495F7 /* HXPhotoPicker-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 8926F9A654DB10F3FDFABCA3C68064A3 /* HXPhotoPicker-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		713F3EE0490A0309288265BB22514DF6 /* AlbumTitleViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3720BD45FD06C5CA7DC5A3A23265349B /* AlbumTitleViewConfiguration.swift */; };
		7196CC6EEA50D7AD1B3C7EF7105BCCBF /* PhotoPickerViewController+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0FAAF2E75DF1E3AD9A49941BE467336B /* PhotoPickerViewController+Preview.swift */; };
		7303206966272F05F00B7E6BE032C4CC /* EditorMaskListProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BA272F2E202985D8D03EEEEBA93BC88 /* EditorMaskListProtocol.swift */; };
		739E93FF0CB3C6E9E21C9E20B10CE8BA /* CustomLanguage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9D0DB1AA5EFAAE3CDE271745B07B02EB /* CustomLanguage.swift */; };
		741EEFE77517A1C9C8E3F265EF63B646 /* ExportPreset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B0E9EEF82C6FE9A3E173E6C2879D0AA /* ExportPreset.swift */; };
		75335FF45AC4A35AFCF60958F1F4C371 /* PhotoAlbumList.swift in Sources */ = {isa = PBXBuildFile; fileRef = C826B4BA45AB750643AEF6B4EF83599C /* PhotoAlbumList.swift */; };
		75AB68A2022875F4634B3C1F1BB61BDB /* ImageContentType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A9E7BD62C42F903F2359F1738E782E76 /* ImageContentType.swift */; };
		75D4882B00DE871F99A5A2EA3CC9D458 /* SwiftPickerResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = 626709B25F4E69020AC52FFEEF2B729A /* SwiftPickerResult.swift */; };
		76DEE5DB7A6D0941766DB24D0ABFB43B /* Core+CGFloat.swift in Sources */ = {isa = PBXBuildFile; fileRef = B98857619009286532DF174050ED3973 /* Core+CGFloat.swift */; };
		772DACCBE150C3D241879CEB365D0C47 /* CameraFilter.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE1A52094CF232757E37725F426CEE59 /* CameraFilter.swift */; };
		795D6732DA90FC01931D4906120EA9F6 /* HXBaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23EFA3CC82F9FC4724D85FFE68B6B000 /* HXBaseViewController.swift */; };
		79E4140EE9CFD67AEE18CA10EC326271 /* PhotoPickerView+Preview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3B49524577C66C9983E155A59E3F32D2 /* PhotoPickerView+Preview.swift */; };
		79EE278DB89DD1E44F8E15116AF1D799 /* AlbumTitleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F30F7A970ADB2A91AD48FC7585596FDE /* AlbumTitleView.swift */; };
		7A15C180B422823246DA43464D8A026C /* PhotoPickerListSwipeSelect.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1667CEE1CB2D196267CD02269FDE9AA3 /* PhotoPickerListSwipeSelect.swift */; };
		7AE18A94ADD5D4872F2AF4EEF7526829 /* PhotoMyAlbumViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F0A5625720E3A09F21AD09CD9B6D696 /* PhotoMyAlbumViewController.swift */; };
		7AF22EC87DC307B49F83F4323933E2D8 /* PhotoTextCancelItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05383888EAD2C595B7B64CED16E364BD /* PhotoTextCancelItemView.swift */; };
		7AF99AA905DC64B642549E80CC498739 /* PhotoBaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0FEA8DDE0F6E15644D3A2F19357F7E23 /* PhotoBaseViewController.swift */; };
		7B1753E366FB609817FB8ECCEF617FDD /* PhotoBrowserInteractiveTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B1FCD59C785A158552AE6C816877FAC /* PhotoBrowserInteractiveTransition.swift */; };
		7B279F06B581B451222AE55F4559860D /* EditorViewControllerDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05C9AF4C66C4630CF97258ECB010F004 /* EditorViewControllerDelegate.swift */; };
		7C58EE3B94159AD15F5DCAAAC22A1EF7 /* PhotoPickerData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BD9A8A607EF9971FC634DB1B34D6D89 /* PhotoPickerData.swift */; };
		7C82CAA3E92BD87D9FB6DF88D35E5002 /* DeniedAuthorizationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B67425251B51353AE8A7D4A38019ED0 /* DeniedAuthorizationView.swift */; };
		7CB94059583173D991DBC559D1A24FED /* EditorDrawView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7B8095CE0780D4129706728A8452A778 /* EditorDrawView.swift */; };
		7CCA45FBE7E631E79B871CFF36DA7DBD /* EditorStickerTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DC32F67FE6674420C8095C93D2E7FE88 /* EditorStickerTextView.swift */; };
		7D2A702D3B139ED2024B1D401C096182 /* EditorMusicListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1FBCD536381D27ED75B173451DAD9C44 /* EditorMusicListViewController.swift */; };
		7EB095945ADF589A965982B119274175 /* AssetResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = FDA004B1012EAEBDCE22DA7ED0AC71B7 /* AssetResult.swift */; };
		7EEB8319D88149893F39AC3E264DC119 /* EditorCollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14C0D4C7CA42AE7C18B6533B5C924568 /* EditorCollectionView.swift */; };
		7EF24D26B2C456519A01415063D64A23 /* AssetManager+ImageURL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31416751C90BDE3CC21B1A4CDDD74C44 /* AssetManager+ImageURL.swift */; };
		7EF42324F1DD7EE0904DF7CD295FF05F /* HXPhotoPicker_Privacy.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 07FCAF745F3EEC27684AB03948F3A3EE /* HXPhotoPicker_Privacy.bundle */; };
		7F233AC0C431B7E1F9053D6E44E2C7D0 /* Core+UITableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BCC0C5B079FF7983B6ACE826D0AB1BE /* Core+UITableView.swift */; };
		7F517F94FB65A9E0D3A6603F9F6287F3 /* PickerCameraViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BC71C725F1D8D2CC11EE2C719F7D0439 /* PickerCameraViewCell.swift */; };
		80F04A9AB10462D7A2E3E24ED021BA73 /* PhotoPreviewContentVideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07ADB42218344B49768EAD997C143320 /* PhotoPreviewContentVideoView.swift */; };
		8149FCCEE03447DCBD111C9077F9E5A1 /* PlayButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC2BB04A00E5980D08522443BE270631 /* PlayButton.swift */; };
		8152B80FB17859F0368E348D19FE31B4 /* PhotoAlbumControllerConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2DF4FBDBAEE565C17A3D83E5B33E95D3 /* PhotoAlbumControllerConfiguration.swift */; };
		815FD6BF258CD19DE928ADDFFF039AE4 /* AssetError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 60F38BD222663EDB7D58414086C7FF80 /* AssetError.swift */; };
		818DCD7706A9C325B478133DF046AEBF /* PhotoPermissionPromptView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3BBE42120AE6117418279AF9153ED501 /* PhotoPermissionPromptView.swift */; };
		82902B4E01A6701B086DC92C40BA20C7 /* PhotoAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0CABA0AC63577DA024753A7B135D2FBF /* PhotoAsset.swift */; };
		8337943C7E955929BCAE38BB2C861CB5 /* PhotoListConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = CFE26E3EDB6CDDE1FDBE203769F6C4F9 /* PhotoListConfiguration.swift */; };
		8530609D5058D8576CE816BA7BF2578B /* AssetURLResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F5F68C9F086246EE2B80A1780765C75 /* AssetURLResult.swift */; };
		862BB21F4029B022C3CC26C5EED36F24 /* PhotoPreviewViewController+CollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64CAB42769526B703114F62BA54294B0 /* PhotoPreviewViewController+CollectionView.swift */; };
		86D0F5B52165FDE0F9E831376CF459CD /* PhotoError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1574B5FA64FB493EA4D959E5BEC4C168 /* PhotoError.swift */; };
		8762360834FA972528DAD859BEDF58D2 /* SwiftPicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = D9042614104217D39CBE9333FE1E93E7 /* SwiftPicker.swift */; };
		87732B623168DFA7D04D5312563C649E /* EditorAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2E87C5D5BA2D5A770503464702C68FE0 /* EditorAsset.swift */; };
		878180707FEC090BD90927E633445223 /* CameraViewController+Result.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0348830034DF98880E51ACBB1694ED51 /* CameraViewController+Result.swift */; };
		89850EC949F6A514447D0534D65800C2 /* PhotoPreviewListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BCCA0B9085E08D1CBBA2ADDA1DEA6B1 /* PhotoPreviewListView.swift */; };
		8991B758B946E9B82B04C939F2E09281 /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6955F08988CC12E37BD1F81EEC8989F0 /* Photos.framework */; };
		8B92DFD720880B41BBD8170E9CACEE9B /* CameraBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 449F6443E35251AD6C63B02F6B0439FE /* CameraBottomView.swift */; };
		8BC24DBBFBD4ECFB51FA93C6B2B5E887 /* PhotoPickerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3EE3D52CE843517C540AD7A187873AC2 /* PhotoPickerViewController.swift */; };
		8C535D2F470AF8228A5D50EC01D413B6 /* HXPhotoPicker.bundle in Resources */ = {isa = PBXBuildFile; fileRef = F7E2222F451C30E1968041519BD47E69 /* HXPhotoPicker.bundle */; };
		8C87884A3F3A000C591C402143BAE4CA /* Picker+LivePhotoTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56048137FCB6FC7A1596045363BCD503 /* Picker+LivePhotoTools.swift */; };
		8DD69D59CE35E3F04C11E928B2DC4686 /* PhotoPickerWeChatViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 93F381FD5C99DE9D1F78DED92F32E1A8 /* PhotoPickerWeChatViewCell.swift */; };
		8E2A7471DF9CC846402188B365F366F5 /* EditorViewController+Mosaic.swift in Sources */ = {isa = PBXBuildFile; fileRef = D81301DEDDBA2299229CA5BE396FAF19 /* EditorViewController+Mosaic.swift */; };
		8EA7150C933B758FE1C0DEA9F3C87431 /* PhotoFetchData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 239166C0B73C4BB5A8DAAC41E96F066E /* PhotoFetchData.swift */; };
		8EBF9511DC5E402FF1FDA0E4FB0E278B /* Editor+PhotoTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F1EDAEE7459431DE287042C93B2FCD9 /* Editor+PhotoTools.swift */; };
		8F17189AFD9923BCBE477AF71C335FFE /* PhotoListCellConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = C837D2DE762765C742523A3290DAB53E /* PhotoListCellConfiguration.swift */; };
		8F2520499806CE1DEAF252E4734EABC4 /* PhotoPickerViewController+FetchAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C8506A89DE0C7792CCF6A4B4934690A /* PhotoPickerViewController+FetchAsset.swift */; };
		8F72EE52BB04220A8FE6D632F88F7235 /* EditorMaskListViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = F88E3547CB6482B8A1478B48B67DC409 /* EditorMaskListViewCell.swift */; };
		8FE081F4602A15EF20A2C92015A41D6B /* ProgressCricleJoinView.swift in Sources */ = {isa = PBXBuildFile; fileRef = AFF094815CCE529B5DCE3302D31E87E0 /* ProgressCricleJoinView.swift */; };
		8FE623BA0D23AF35529A5AE14400506A /* CameraControllerProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21203AADFE60A2EAA646679854B350D0 /* CameraControllerProtocol.swift */; };
		9109363DC5A5846B73F441A1303AD2B2 /* PhotoBrowserInteractiveAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3143AC72F045E962D820FECD4E6A918 /* PhotoBrowserInteractiveAnimator.swift */; };
		912F612A9FCA035C2A372402162D64F1 /* PhotoHUDConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2368A59124CA95605FB5D47B60CDD613 /* PhotoHUDConfig.swift */; };
		916FB428CB6D4FC61E4FDD12B50793E5 /* PhotoBrowserAnimationTransitioning.swift in Sources */ = {isa = PBXBuildFile; fileRef = ABF3D7D7A51DF564BC0A442577BF519F /* PhotoBrowserAnimationTransitioning.swift */; };
		92CE1FF5026522FA76C89192892EBE85 /* VideoPlaySliderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44A2076CF8BCEE5613EC7FE0CDF56CCB /* VideoPlaySliderView.swift */; };
		9366D0507CA28C6693D3601FE868FFC7 /* Core+NSObject.swift in Sources */ = {isa = PBXBuildFile; fileRef = E48588F47A38757CB1F151D71B4230FE /* Core+NSObject.swift */; };
		94AE193C1BDCC2E3074AA1837474A977 /* EditorMaskListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A20059888303B51E7892486BC6B003A /* EditorMaskListViewController.swift */; };
		95302EB74A0B90067AD3B32143226CDD /* AssetManager+AVAssetExportSession.swift in Sources */ = {isa = PBXBuildFile; fileRef = A54D7406174AC4D0CE3EFDE0B53751EF /* AssetManager+AVAssetExportSession.swift */; };
		96693A9BC71203F814383B8B6879305E /* EditorView+AVAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 02E3FCCEFC4531542946F1FD06749773 /* EditorView+AVAsset.swift */; };
		973C3A4B017B542E60025DEBDE7A2830 /* EditorPlayAuido.swift in Sources */ = {isa = PBXBuildFile; fileRef = E84FC07A93C5FC648633FD090EFCA2B3 /* EditorPlayAuido.swift */; };
		975470A1A55498FCF2C3F22CEAA402F4 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CA0FA23181E1B0F518C7A51B4343E598 /* Foundation.framework */; };
		98AB7AED667FB8772D59524B7D66F86F /* PhotoBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1EBE3C61A5B2F6140C339912406657D /* PhotoBrowser.swift */; };
		98F97D26DCE55075E031FC3E98ADBDEF /* AlbumSectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A169FA30F6F6FCCB8017CE00D7336EF /* AlbumSectionHeaderView.swift */; };
		99E438474C1A294A21BE3D736FDBC301 /* PickerBottomViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8541F4B9A66C5874778B820DD6761138 /* PickerBottomViewConfiguration.swift */; };
		9A11D11FCD16DCD0A542B39BCC7BF759 /* PhotoToolBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 243CAB4B07D2DF72404C1C9E31DB06C1 /* PhotoToolBar.swift */; };
		9A1D31F601A3B9598D7CA682C4EFFEA1 /* EditorViewController+Await.swift in Sources */ = {isa = PBXBuildFile; fileRef = E6196CCBA9D405C4C3168A30537299D9 /* EditorViewController+Await.swift */; };
		9B33DD8A840AD16FA2741A857CD153CF /* EditorChartletListProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = B35AC25D27FECE53721F41B14ADBEB6C /* EditorChartletListProtocol.swift */; };
		9BD9452635BB2E5A945DDC33D588F489 /* Camera+PhotoTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = A8A6A747A1356D5A504D90EC7A4A9C0D /* Camera+PhotoTools.swift */; };
		9C9606B61F983E79791542183A0D0EC1 /* PhotoTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20DE3E2E1350F88459A354CCF605AFA1 /* PhotoTools.swift */; };
		9DBE35CC02D5093C72CEE796F07D3048 /* LanguageType.swift in Sources */ = {isa = PBXBuildFile; fileRef = ADA0AE8C603E25DB2343D5C9900A4D1B /* LanguageType.swift */; };
		9DD22BFB5831316C9FFD6BF1CEF04C12 /* PhotoSplitViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D0285F28163890D20C1C5FAA3E5E506 /* PhotoSplitViewController.swift */; };
		9EFE7DEE1E6D59C5B757941AFC4FCE9A /* PhotoPickerNavigationTitle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74E7EB7A97AE8D6A4851E9E76EE5DC8F /* PhotoPickerNavigationTitle.swift */; };
		9F72019EF49EFFE6684FDC487897852A /* EditorAdjusterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8B990D88767583534A7DA8B3F06FEA2 /* EditorAdjusterView.swift */; };
		9F8EA645A79528A3021D67BA136B1205 /* Core+UIDevice.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1F30093DC54240D9DDE7A029AD8218FE /* Core+UIDevice.swift */; };
		A0148A0989897F63A4DEDD6B4FCCF751 /* EditorStickerTextViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 70E207315CC6F2FE9AB3FD99F0B5D602 /* EditorStickerTextViewController.swift */; };
		A09385B03BA422F5D86EB388A1DFE001 /* Core+UIImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8222B901A5D698452D79E5B5956FFC34 /* Core+UIImageView.swift */; };
		A179A1208006CC2B578CA98F42B9CE34 /* EditorStickerTextView+CollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3BF6FFD36D652250E746F7300522BE42 /* EditorStickerTextView+CollectionView.swift */; };
		A2834EEB99A13624B55D293CA51B50EC /* SwiftPickerConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B8CF63B8795710CACC35F1BB469DD99 /* SwiftPickerConfiguration.swift */; };
		A394643D4B98317FD3074AEACEA3BC22 /* CameraViewController+BottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C4626E08C6D70CDA42B1716225D94F6 /* CameraViewController+BottomView.swift */; };
		A3B1967606AC546BDE90F6D69E723A28 /* PhotoAsset+URL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E71B2434C727860F1D0634C3C22CD1C /* PhotoAsset+URL.swift */; };
		A47F066D8AF04455D29C0EFD193DFCA0 /* PhotosUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E4C4C22A0FB5ACB79DC6700191E54B09 /* PhotosUI.framework */; };
		A4D2B60779B2257B1A5A8A47FD45A1EA /* EditorFrameView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 681EDFAA6E48BA557A3C85A1C4F0228F /* EditorFrameView.swift */; };
		A596001CD21970438E71DC6288238358 /* PhotoPickerControllerInteractiveTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C7C4B5CE5C2BD1DB973232A2C4F251C /* PhotoPickerControllerInteractiveTransition.swift */; };
		A6418737B21B2F9BCEC1A319D56C3D3E /* PhotoManager+Language.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C443D9D41A72D31A6C6C3FFE92A6C57 /* PhotoManager+Language.swift */; };
		A70BF0131CC661B468DA3E2CA8FFAAA6 /* SelectBoxConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0D2A755EE3FB70F378E2A108DBDE0388 /* SelectBoxConfiguration.swift */; };
		******************************** /* HXCollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = EEFD90C3AA11B20E0C3458043619A03B /* HXCollectionView.swift */; };
		A7E3DBAEF90907EDC99CC73F57556259 /* EditorStickersItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7ADA493E783217B491C3B3F97D4A417C /* EditorStickersItemView.swift */; };
		A80314EA5D7580B1D95049630AECDB9D /* PhotoPickerView+Asset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C0DF4B5D571D1B934FA77506277EE0A /* PhotoPickerView+Asset.swift */; };
		AAEBB06DA59DD58633B0A7BFACF9615A /* ProgressIndefiniteView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 374D8409ED6472B64DBB97DD92BF1BE1 /* ProgressIndefiniteView.swift */; };
		ABC36F1C75B1173B2940517446388911 /* EditorBrushSizeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5B92DF75FC596DE9EB8FC8D2DE44D29 /* EditorBrushSizeView.swift */; };
		AC035AEF5390BB1D2502B3AAB69790FE /* DeviceOrientationHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BCC1E9AB6067A64C54EA5A0166CFB42 /* DeviceOrientationHelper.swift */; };
		ACD51E7F2A3E7D07D13252C0B462DFCE /* PhotoPreviewListViewLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 388B0C7C04F8004672033964FF13F190 /* PhotoPreviewListViewLayout.swift */; };
		ADF5C143C6BC2DEA4702A3B87C6938C1 /* IndicatorType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 33129BB49305C38EAF061BBFF4398D46 /* IndicatorType.swift */; };
		AE01DD1C5BC26F45EB977264E9942B22 /* HXPhotoPicker-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 201F43E6FB714D2F12BB4715611219AD /* HXPhotoPicker-dummy.m */; };
		AFA710AB2E2E0542F6E9B1669F1CD9D1 /* PhotoManager+Download.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00F354CA817B91147A2B6664B3B3C165 /* PhotoManager+Download.swift */; };
		B2BEDEC2D83ED7224E94CFC4DCA37F95 /* VideoEditedResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B6F78E40C3969BB4BF56C347EAF9C7 /* VideoEditedResult.swift */; };
		B2C9D17C775C2F536D15E8F45D147202 /* EditorVideoControlMaskView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22A6778713FB9291D095FC6C2CF15E5D /* EditorVideoControlMaskView.swift */; };
		B3FBC8C040A79E2761D011730D6F9262 /* EditorType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11A24B2CA6251FE86963A233AA476998 /* EditorType.swift */; };
		B3FC87F1679B685740D7CE20080E7627 /* Picker+Array.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD9ACD2CC4741B6C2EC865B1B37E93FD /* Picker+Array.swift */; };
		B49257B71B0D87C2D05EDC5DD7F73AB4 /* PhotoPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7E8C4AACC66454A525FB1243A9325B /* PhotoPickerView.swift */; };
		B50CFB59D5CA81D69694D847CBF20BAE /* EditorAdjusterView+Croper.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7F242A5DB974C77A812451336E03077 /* EditorAdjusterView+Croper.swift */; };
		B5C9079457DBC794B89E384BDE0580EC /* EditorViewController+Filters.swift in Sources */ = {isa = PBXBuildFile; fileRef = 536BA9E2E17F41F53A01D6CB8C28DA14 /* EditorViewController+Filters.swift */; };
		B63ECAC7932BEA42C045C4BCF8C2CF74 /* PhotoDebugLogsConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = D8256C64E6C752D404E0A9C34C5D1BE3 /* PhotoDebugLogsConfig.swift */; };
		B70066F73A360DDF67B6D77C3F2A5871 /* EditorView+Public.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1C2D2125819A59968D4F278869202D4 /* EditorView+Public.swift */; };
		B7FA7A8220ADC24C96C3BAD3CF302722 /* Core+UIColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = F5A28B03430782E79CF5B5A9821A53E5 /* Core+UIColor.swift */; };
		B8FEB48433BA54DEF6B701723E39032A /* PhotoAsset+Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = B9CAC87B32E89186965BB55B63A5E28E /* PhotoAsset+Request.swift */; };
		B934198CFA22CFFE128AFCAD9652F5D8 /* LocalAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D064ADDE951DF2CCACF53E53E87982C /* LocalAsset.swift */; };
		B9C6F9D1EB6E3EDDDE1E2D33B5A8E76B /* Core+UIViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5EDDD0D27D73280213454C9A65E4F643 /* Core+UIViewController.swift */; };
		BA25B92A54CAB0C8D697DCD7C100BD84 /* PhotoAsset+Codable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2A27B00D1A8D0609645BA96BDECDF6D0 /* PhotoAsset+Codable.swift */; };
		BCFB0D4A14D2BB4EC4EB87F643035BB8 /* CameraManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4405AE97A7290BF05BE6EBEDD921068B /* CameraManager.swift */; };
		BD1879EDC9A5214B2F3D00B0284B225A /* AlbumListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DBDA4EE84FBF358E9F6F772CC945A8C6 /* AlbumListView.swift */; };
		BD827A4B5621011B6FC1B970BD7723D9 /* PhotoAsset+Network.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6AF672E111C559E9FA004AB385D877C /* PhotoAsset+Network.swift */; };
		BF0D4E7C35C57FCE98B254DC01FA2B43 /* PhotoAsset+FileSize.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB4D8A08D155332EA4B9A3B8721804E8 /* PhotoAsset+FileSize.swift */; };
		C0275FD4898DECB0AC51679735A4B36A /* PhotoPickerViewController+AlbumView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1FFE3F1461D4D7A2F119186A8080AEE5 /* PhotoPickerViewController+AlbumView.swift */; };
		C0AD4C8F8A76E446038D94B6841579A2 /* PreviewViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 02F7F69D8D5303057827F2CCCDFC14B4 /* PreviewViewConfiguration.swift */; };
		C0F5894C7DFB09DBFE7FF3EC59346BBC /* PhotoPickerViewProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C4F0A230FC76EEAAB91AA8CFAFEE25A /* PhotoPickerViewProtocol.swift */; };
		C0F9A1A3499589CC1450A7956FAAE673 /* EditorFilterEditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38931FA28AE376649ACC359D39A0FE1A /* EditorFilterEditView.swift */; };
		C0FA470D66C93E671A1897EEBC5FEFA7 /* Core+Dictionary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 221550860117559284BE64A15F040FDF /* Core+Dictionary.swift */; };
		C1E60A83D3C0668FBA5518FC4107A63C /* SystemCameraConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3D93E8511537AE296CC1F4787ED520D1 /* SystemCameraConfiguration.swift */; };
		C343C5E885B0BE1F51F64212AF8BC8FB /* PhotoNavigationItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31DE782466BE5DE2396CBB4DA16CE0D1 /* PhotoNavigationItem.swift */; };
		C3CC1D806A559D613DE44F513F45F079 /* PhotoControllerEvent.swift in Sources */ = {isa = PBXBuildFile; fileRef = E647DC1FA5D26CE60119315DB1DE4A49 /* PhotoControllerEvent.swift */; };
		C4101AFE632FA075EE8C5D520873DA89 /* Core+LayerRTLFrame.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3E17A40D70CC2BE7E99CEC723827E338 /* Core+LayerRTLFrame.swift */; };
		C5001A1304C58E3BDEAA4F5DB5C319B0 /* AssetManager+ImageData.swift in Sources */ = {isa = PBXBuildFile; fileRef = B51CCD9558282BD278E2FE04C5E5D55C /* AssetManager+ImageData.swift */; };
		C55285E884784847935EEC6F764B17D6 /* Core+AVAsset.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19A0A8F9EB0E61AE1A79E72DC25B6615 /* Core+AVAsset.swift */; };
		C5ACDDD44CB1F33C6EF09BEF7AB8840B /* EditorStickersContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D3940325C2B5A170D6AC310F478165C5 /* EditorStickersContentView.swift */; };
		C6480A82634E148954A108AE3958F267 /* EditorAdjusterView+Mirror.swift in Sources */ = {isa = PBXBuildFile; fileRef = A3F574521FD9FE506349F7BD4C56E601 /* EditorAdjusterView+Mirror.swift */; };
		C6BCB08865F4E8F3EDE42428A8E756D5 /* Core+UICollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90E70CD912661F1824EF4ABC6B1A2682 /* Core+UICollectionView.swift */; };
		C744A3FAB547D00EDF836522E7CBBFE1 /* Pods-ObjCSwiftDemo-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = EC257FD53CE9981794E1907003750498 /* Pods-ObjCSwiftDemo-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C76EA0805516EB24D9F19A7D5B35F3B4 /* PhotoPreviewViewController+SelectBox.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7771F644EC541C4B1928477071709DC9 /* PhotoPreviewViewController+SelectBox.swift */; };
		******************************** /* EditorRatioToolView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1069D2AE707CDAE357CAA9F6A39BFA /* EditorRatioToolView.swift */; };
		CA6938E564C96FAD052B3750A5158E66 /* SystemCameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDE9435DDA3E22DE1F324BC6E9FB97D7 /* SystemCameraViewController.swift */; };
		CB69E7D5F01FCAE91E8E068588318164 /* PhotoPickerController+PHPhotoLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 570A91174D6C4846D15DC63C0184C1C5 /* PhotoPickerController+PHPhotoLibrary.swift */; };
		CB9497B1D8C7B271C38D20B2139AB933 /* EditorStickersTrashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D400C8DE7A3AA4A130C23BD14B828E9 /* EditorStickersTrashView.swift */; };
		CC06E4A67712CDA8949753FD9542C61C /* PhotoPickerListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5BF8B40CF7294D1A6B830858A212D16F /* PhotoPickerListViewController.swift */; };
		CC4590878680CFFA690B6955A0A6D380 /* EditorChartletViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0088A37BB233E37D2F269B435A77AF93 /* EditorChartletViewController.swift */; };
		CD193461C847ADFDC3D79CEE1189D218 /* PreviewVideoControlViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = CAE2B41F0BC0E7EE8ECB33D79F45B790 /* PreviewVideoControlViewCell.swift */; };
		CD4DAC4E58C079C0A9D7F64E6B17CAF5 /* PreviewPhotoViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F93BD37C3D8479C651D62C436F4981F /* PreviewPhotoViewCell.swift */; };
		CDAAFF4B64D115A72C11D0840BE5B404 /* Picker+PhotoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4FD1C4E06593919519B1D9F580D76EF4 /* Picker+PhotoManager.swift */; };
		CEA508576B639F39C839383E7640E569 /* EditorViewController+Ratio.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB2E9327281C7E6ADAAFA50519FBD70A /* EditorViewController+Ratio.swift */; };
		CF187822C94A59308FCE3124FA5F121A /* PhotoPreviewViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 285F38BF41ACC86F4631E429089F69E8 /* PhotoPreviewViewCell.swift */; };
		CF3106440DB6D5E0EBB0AB3DB6D5E495 /* EditorAdjusterView+FrameView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22CD92FB8531CBB61F425912330F0C19 /* EditorAdjusterView+FrameView.swift */; };
		D1078D485B3E3E4446AEF9C8560ED790 /* PhotoPickerList.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBA12623DE8AE03143F3AA7E76EBCC54 /* PhotoPickerList.swift */; };
		D1A44D3825989315B3B6C888C9D420DC /* JDTImageModule-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 8D59106DE26A2FDE8E2B0BCFFBF2388F /* JDTImageModule-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D1A47923A25AB21A2B9379CF85862A92 /* ProgressHUD.swift in Sources */ = {isa = PBXBuildFile; fileRef = 891154F04051668B1F84BE657A8DC04B /* ProgressHUD.swift */; };
		D1A759D6A993F81CCE366E7D211F02D3 /* EditorViewController+Text.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7E23569E056ABA66F7622E1289D28A74 /* EditorViewController+Text.swift */; };
		D2B569F602F65E24158BF8AD615D70B0 /* Core+UIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63A0C8A37013E36CAE04B87E2D50A6F2 /* Core+UIImage.swift */; };
		D2D07C1D3018405BA61AC66C61369B1A /* PhotoAsset+Video.swift in Sources */ = {isa = PBXBuildFile; fileRef = D8A088AAD9846B0B3B4212A8782C8698 /* PhotoAsset+Video.swift */; };
		D3C11D38AACB7DF788CEE332857D6F21 /* JDTImageModule.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 881094C4979E410E6AF6E86318DA4F98 /* JDTImageModule.bundle */; };
		D41BDBD73DB5EEA0D597C8333861F766 /* PreviewVideoViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 656BC291DE4861E74F84D666CB0F469B /* PreviewVideoViewCell.swift */; };
		D5E204519AF6AD02811202D4C93A8EDC /* Core+String.swift in Sources */ = {isa = PBXBuildFile; fileRef = FE2B5106FE3DAED048A89AC0B4DAEB7C /* Core+String.swift */; };
		D7ABE4149788AD31C1D6642A4AFA059F /* PhotoPickerView+CollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7A7EC1CE5A1231D6A9C716A27FB4B7CD /* PhotoPickerView+CollectionView.swift */; };
		D7DD623490F2A9EB781D8877C7895888 /* CameraViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = C13D44321CF536452C258FA91D0591BB /* CameraViewController.swift */; };
		D8BBD6959FE880C14AF447F36F2445D2 /* PhotoLoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C432D845FDB8DD800DC0301FF20E0FE /* PhotoLoadingView.swift */; };
		D93429515CF44DEA2264CCF750463ACC /* Core+UIFont.swift in Sources */ = {isa = PBXBuildFile; fileRef = 354F52A0F0044AE20E410A7D676F143A /* Core+UIFont.swift */; };
		DA2E4FB83B640AE58D8138A6400BDF21 /* EditorView+PhotoTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = D72613C404D0409B2BBA480A1EA31504 /* EditorView+PhotoTools.swift */; };
		DA885778DF3078434197D9F83333987C /* Core+FileManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7984854A9E6B4963A93F19EBC7B68D5C /* Core+FileManager.swift */; };
		DD506E173690C191669E9C6EF26B7B62 /* AssetManager+VideoURL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7FA386A2CAFDE68CBA7593DD43D27286 /* AssetManager+VideoURL.swift */; };
		DDF9F3D8AE2EC839F87FD501EBFCB57D /* EditorVideoControlView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5AD5023CDB2089BBD3F7CEBB26C4B057 /* EditorVideoControlView.swift */; };
		DE32DA670AC1FD7CA047F44CB61B2399 /* EditorViewProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 543EABD5688C99ED261487FEA57C272C /* EditorViewProtocol.swift */; };
		DEB754BC853D84A4EE962D54960DF870 /* Core+UIView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D7A1AF7E3D9B42D128F2F201E985E4D /* Core+UIView.swift */; };
		DEEFBB403A2C3E2A4D39954392523996 /* CameraViewController+Location.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9048CFCB871D7170D4EEB7A847CB0C /* CameraViewController+Location.swift */; };
		E017E7FAA34D6C1AB8FBDE40368E07BC /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36A40A4132BAAA6ECA9020806AA973BF /* UIKit.framework */; };
		E0587AC566F7B3CE7DE1B71FCD19CE41 /* AppearanceStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 807C4D50EA3E351ECA6D86909712769C /* AppearanceStyle.swift */; };
		E124A926E123F29808AFFB174137BEA4 /* PickerTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = E839F48FFB861BAFC90C07D57D3B44C1 /* PickerTypes.swift */; };
		E17633D3EDE9ED20B106BE131844780B /* Pods-ObjCSwiftDemo-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = C5D3F303175A23D5817E0B910B39D591 /* Pods-ObjCSwiftDemo-dummy.m */; };
		E2CBDFEE6392CB9661667AABAA1CC0FC /* EditorViewController+Brush.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B1250D07BCCD70BD33720C6E4FFE6B9 /* EditorViewController+Brush.swift */; };
		E3652AC8F91342221FF1E5F8A1D60ED3 /* PhotoPickerViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = E0830D19A6308315D771E8A1ED741D76 /* PhotoPickerViewCell.swift */; };
		E3819A9FF4F8A57BC5F58D0182377C43 /* PhotoPickerViewController+Editor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9DA28EC100612F848DA85881AC52C0C0 /* PhotoPickerViewController+Editor.swift */; };
		E3DC636E1EE6A0B77B008DF8F0372EBA /* EditorAdjusterView+Edit.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE7EDBEA358AB872EABC06AAE5CB0CB1 /* EditorAdjusterView+Edit.swift */; };
		E3E3360B3DF862514475FC1B03404A7B /* JDTImageModule-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = B4EC2827755DE52F18E04B210B41E1C4 /* JDTImageModule-dummy.m */; };
		E643919B802A76207AFFE10C21ED1B13 /* CameraRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 92D70113DE6E938C6208B194A4DF7FF1 /* CameraRenderer.swift */; };
		E66978FD9F09E0DC0140ED2338C7E6F1 /* AssetManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = D57B0ECA4C90696B5784C169B591ADDD /* AssetManager.swift */; };
		E753679E39943D1BE0CE8A45D594760D /* PhotoAlbumViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8FC093A59283E28FE8F6F342859945C9 /* PhotoAlbumViewCell.swift */; };
		E8C177370E7DBBE801F10F7502A1B723 /* PickerTransition.swift in Sources */ = {isa = PBXBuildFile; fileRef = DBF247F9682F6DB63727C57A707D5DB1 /* PickerTransition.swift */; };
		E92A724ED7CCE729F1FF19766994F8BA /* PhotoPickerListCollectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5B278228584CE2103FF6C1F876E4B3E1 /* PhotoPickerListCollectionView.swift */; };
		E9A33DB21AF9F47CEFB7B2127E940A3E /* Picker+UIViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = D73B3F385CEC6393D0C48C73B152A263 /* Picker+UIViewController.swift */; };
		E9AB38B55E5E2702C323A706C6599D45 /* PhotoPickerBottomNumberView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 70530C3EEB787DC2042A8C2C771C312D /* PhotoPickerBottomNumberView.swift */; };
		E9B7A969A4C550B149CD71F667CDF55E /* PhotoTools+Alert.swift in Sources */ = {isa = PBXBuildFile; fileRef = D56C056B4F68F3C6A1361F436E9F0B46 /* PhotoTools+Alert.swift */; };
		EA0E43FD97F57A056C6B15704ACDD983 /* LivePhotoError.swift in Sources */ = {isa = PBXBuildFile; fileRef = A4DAECBC434D0EEB28E1B2D18954AD67 /* LivePhotoError.swift */; };
		EA1842EBD935C133E39A7B031277DDBF /* AssetManager+Asset.swift in Sources */ = {isa = PBXBuildFile; fileRef = CFA90E32D003CA214BE709B7198AA294 /* AssetManager+Asset.swift */; };
		EB8A3C65830F0EBFC1D85900B0502D4B /* EditorScaleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1733B9CE45A9CC36C6FBFF922A0D3C8F /* EditorScaleView.swift */; };
		EBE3302A8962007DA08E11A204F929FF /* Picker+ConfigExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 265C677F21C6633D856E4F28AD8A1EFD /* Picker+ConfigExtension.swift */; };
		ED2CBCE10B26E8F8533DBB6C3F4B569A /* PhotoPickerListConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = D35F35F5F6F97DECE3DA8DF781DDC37C /* PhotoPickerListConfig.swift */; };
		ED7D28898742BFD89DAA7A11D0F2A31A /* PhotoDeniedAuthorization.swift in Sources */ = {isa = PBXBuildFile; fileRef = C56BF509892A4AF72B38428504A6C43F /* PhotoDeniedAuthorization.swift */; };
		EE8B0F1FB701065D5134B010DF577F1D /* Core+URL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A9B2FC917F92187A663B16A27348A57 /* Core+URL.swift */; };
		EE95332D25230451A057BB12C9DE3594 /* ImageEditedResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24B4FE0BE0C6D74C73084CD933B63758 /* ImageEditedResult.swift */; };
		EF2E0B3584AFDDB154B39CE862028859 /* PhotoPickerFilterItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D25073787F2C34465FBD89072A0C8AF /* PhotoPickerFilterItemView.swift */; };
		EFCC4F5512BA26F5F6A55A30D8C03D66 /* PhotoPickerPageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6121E4C9EE4DAB7308F516C45E8E22E /* PhotoPickerPageViewController.swift */; };
		F2698785BEA19B505A5453CE03449EC0 /* ProgressCircleView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4350B462D8302145E8EA459D1693033D /* ProgressCircleView.swift */; };
		F2CDFD58D342C35A967C6DBA7F7F8353 /* EditorViewController+EditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 02F9DDF2E790E5CF5F5C31CCE85BA953 /* EditorViewController+EditorView.swift */; };
		F56C0CBAC05DD479D08DB14EAA6F46C4 /* Core+CALayer.swift in Sources */ = {isa = PBXBuildFile; fileRef = F63525793BC65420295CDA52FFC23D86 /* Core+CALayer.swift */; };
		F56DB1B780C1236FBCE37C0E29AFFD82 /* PhotoPickerController+Transitioning.swift in Sources */ = {isa = PBXBuildFile; fileRef = ACBF2803ADC011D484A1A508DB226C45 /* PhotoPickerController+Transitioning.swift */; };
		F58737A0040FBD8B44344F23444F3051 /* Picker+UIImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 710B03E0738E38F4038E5859A93B18B4 /* Picker+UIImageView.swift */; };
		F5FA75788784A5FF88770412183337F0 /* AlbumViewBaseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9C3C572B7347BC237964D184C0D614E /* AlbumViewBaseCell.swift */; };
		F658582934F37E43E877D8D96DDEE687 /* AssetSaveUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3E681373CE7E092A140F20821A4BE159 /* AssetSaveUtil.swift */; };
		F808B701E06B76D7CE934FC246F53390 /* PhotoAlbumViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD928A7EDEC6704F7620C56CDCE82173 /* PhotoAlbumViewController.swift */; };
		F83382C6EE2405CF547E3BE0C9DDE26F /* PhotoAlbumCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 91756DAFF4884D2D3E0C9BAA43248DAF /* PhotoAlbumCollectionViewCell.swift */; };
		F855FE7853CFD6868FA6884BF3998680 /* PhotoPickerViewController+PhotoList.swift in Sources */ = {isa = PBXBuildFile; fileRef = E699FA4A5EE48FF91EBB54DECC895251 /* PhotoPickerViewController+PhotoList.swift */; };
		F8F2CA31D1B68DE6E7E15FC87190BBD1 /* EditorView+CGFloat.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB77DD56A5062F7655E8C4E992737F63 /* EditorView+CGFloat.swift */; };
		F94A66C4EB7FC8F06905DDEBC16AA18A /* PhotoPickerDataStatus.swift in Sources */ = {isa = PBXBuildFile; fileRef = 544413451FE214084FFE9FEC3F1ECE34 /* PhotoPickerDataStatus.swift */; };
		F961224500BF1B334159CB9536B4A220 /* EditorTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = A381EBE707A2E2E5D34AF239D383538D /* EditorTypes.swift */; };
		F97B125BD2CE4546C771BB156D4D9EA5 /* PhotoPickerController+Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = C2C358EB7A8F7B2162E977322E2FD4E4 /* PhotoPickerController+Internal.swift */; };
		F99C4C0D48B240209FDF7370288C2A31 /* EditorToolsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ED6704C7247DAD1DC24FF6D2E21B1CF6 /* EditorToolsView.swift */; };
		F9F4C43AFD00FD41ACF786EFCD7A6ED9 /* EditorView+CIImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 80D6AA4452E70310081F4712F4BFA513 /* EditorView+CIImage.swift */; };
		FA54A90AE492560C1FE96E5B7A02AD79 /* PhotoToolBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E8FAB15BFAE7A24E7577FB03B793FA6D /* PhotoToolBarView.swift */; };
		FB4749C83670D63D7F4EDFC6E2EC7FE2 /* PhotoPickerView+Cell.swift in Sources */ = {isa = PBXBuildFile; fileRef = D23429C610772852F9E743D8AA00E035 /* PhotoPickerView+Cell.swift */; };
		FC92FE6816B5C4DB5D3354EC95A6E372 /* EditorAdjusterViewProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE8B055FA8006491BCABC86DC2D0EA75 /* EditorAdjusterViewProtocol.swift */; };
		FCB73B156077823F30123D8511E1D4C6 /* PhotoPickerView+Function.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F251867819303E98BCB2ACAC5669D20 /* PhotoPickerView+Function.swift */; };
		FD188CF44C89C97E16B4A853C488893D /* PhotoHUDProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D5B847B39B717ADFF6273BE06162347 /* PhotoHUDProtocol.swift */; };
		FE1F1D76EF579E5C02B7132CFD68C6A2 /* EditorRatioToolViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 558E84A3D3BB9CCDFA22EFE5D4472960 /* EditorRatioToolViewCell.swift */; };
		FEB11D68761B46F6A3DF6B9C3A85D83C /* EditorView+ScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65A82F18C2977BBCF29FF2583045734A /* EditorView+ScrollView.swift */; };
		******************************** /* SelectBoxView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* SelectBoxView.swift */; };
		******************************** /* EditorDrawTool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1B712293FCB50420C681131E67997887 /* EditorDrawTool.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		85BDEF53F401F1CC3813C0D4E070085C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4A04CD391AA4051BA344312125D5AAD5;
			remoteInfo = "HXPhotoPicker-HXPhotoPicker_Privacy";
		};
		91EAFA8DC81FF570C580B141D9B5E7CB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4CAE7BADEF6DEA6F9871EE05A09D90E6;
			remoteInfo = JDTImageModule;
		};
		93BC09FAE3869F52D063564CA6D5FAD3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F8051AA643C524FA4E210DD0E6E62332;
			remoteInfo = HXPhotoPicker;
		};
		AE19D09E742F40D77AD90288A608C0DB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F8051AA643C524FA4E210DD0E6E62332;
			remoteInfo = HXPhotoPicker;
		};
		FE3F564F64ECBB4E5D559296438FD67C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 229C8D8B66D775381ACB6E3EFE9D0354;
			remoteInfo = "JDTImageModule-JDTImageModule";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00646E2E29A24BF95CE89A4A1C731698 /* HXPhotoPicker.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = HXPhotoPicker.debug.xcconfig; sourceTree = "<group>"; };
		0088A37BB233E37D2F269B435A77AF93 /* EditorChartletViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorChartletViewController.swift; path = Sources/HXPhotoPicker/Editor/Controller/EditorChartletViewController.swift; sourceTree = "<group>"; };
		00C43FF90F67084C11C94811FA62D7E9 /* EditorCanvasView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorCanvasView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorCanvasView.swift"; sourceTree = "<group>"; };
		00F354CA817B91147A2B6664B3B3C165 /* PhotoManager+Download.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoManager+Download.swift"; path = "Sources/HXPhotoPicker/Core/Util/PhotoManager+Download.swift"; sourceTree = "<group>"; };
		02E3FCCEFC4531542946F1FD06749773 /* EditorView+AVAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+AVAsset.swift"; path = "Sources/HXPhotoPicker/Editor+View/Extension/EditorView+AVAsset.swift"; sourceTree = "<group>"; };
		02F7F69D8D5303057827F2CCCDFC14B4 /* PreviewViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewViewConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/PreviewViewConfiguration.swift; sourceTree = "<group>"; };
		02F9DDF2E790E5CF5F5C31CCE85BA953 /* EditorViewController+EditorView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+EditorView.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+EditorView.swift"; sourceTree = "<group>"; };
		0346A114CDA8038E97C997FEAB11F808 /* EditorViewController+LoadAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+LoadAsset.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+LoadAsset.swift"; sourceTree = "<group>"; };
		0348830034DF98880E51ACBB1694ED51 /* CameraViewController+Result.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CameraViewController+Result.swift"; path = "Sources/HXPhotoPicker/Camera/Controller/CameraViewController+Result.swift"; sourceTree = "<group>"; };
		0379962A2D6FAADB683611C2271421AB /* AlbumViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Album/AlbumViewController.swift; sourceTree = "<group>"; };
		03841F568EDB504253E5A557B4B435BD /* PhotoToolBarEmptyView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoToolBarEmptyView.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoToolBarEmptyView.swift; sourceTree = "<group>"; };
		05383888EAD2C595B7B64CED16E364BD /* PhotoTextCancelItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoTextCancelItemView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoTextCancelItemView.swift; sourceTree = "<group>"; };
		05C9AF4C66C4630CF97258ECB010F004 /* EditorViewControllerDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorViewControllerDelegate.swift; path = Sources/HXPhotoPicker/Editor/Controller/EditorViewControllerDelegate.swift; sourceTree = "<group>"; };
		071D0BA493A9B71B143937D27E4E2D3B /* HXPhotoPicker-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "HXPhotoPicker-Info.plist"; sourceTree = "<group>"; };
		07ADB42218344B49768EAD997C143320 /* PhotoPreviewContentVideoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewContentVideoView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPreviewContentVideoView.swift; sourceTree = "<group>"; };
		07FCAF745F3EEC27684AB03948F3A3EE /* HXPhotoPicker_Privacy.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = HXPhotoPicker_Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		094DC497053AE73D659B6748B85D89CF /* PhotoPickerControllerProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerControllerProtocol.swift; path = Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoPickerControllerProtocol.swift; sourceTree = "<group>"; };
		0978A23FA7F89FCF52E275CA641B3941 /* EmptyViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EmptyViewConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/EmptyViewConfiguration.swift; sourceTree = "<group>"; };
		0CABA0AC63577DA024753A7B135D2FBF /* PhotoAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAsset.swift; path = Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset.swift; sourceTree = "<group>"; };
		0D2A755EE3FB70F378E2A108DBDE0388 /* SelectBoxConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SelectBoxConfiguration.swift; path = Sources/HXPhotoPicker/Core/Config/SelectBoxConfiguration.swift; sourceTree = "<group>"; };
		0ED4F6C9D4069955665F6EBF056A37A9 /* EditorContentView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorContentView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorContentView.swift"; sourceTree = "<group>"; };
		0FAAF2E75DF1E3AD9A49941BE467336B /* PhotoPickerViewController+Preview.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+Preview.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+Preview.swift"; sourceTree = "<group>"; };
		0FEA8DDE0F6E15644D3A2F19357F7E23 /* PhotoBaseViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoBaseViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoBaseViewController.swift; sourceTree = "<group>"; };
		11A24B2CA6251FE86963A233AA476998 /* EditorType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorType.swift; path = Sources/HXPhotoPicker/Editor/Model/EditorType.swift; sourceTree = "<group>"; };
		11D34D65AFB3F2CD87764A64C97F93B7 /* EditorTransition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorTransition.swift; path = Sources/HXPhotoPicker/Editor/Transition/EditorTransition.swift; sourceTree = "<group>"; };
		14C0D4C7CA42AE7C18B6533B5C924568 /* EditorCollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorCollectionView.swift; path = Sources/HXPhotoPicker/Editor/View/EditorCollectionView.swift; sourceTree = "<group>"; };
		1574B5FA64FB493EA4D959E5BEC4C168 /* PhotoError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoError.swift; path = Sources/HXPhotoPicker/Picker/Model/PhotoError.swift; sourceTree = "<group>"; };
		165848B829E7D377966C7F5A8418690C /* EditorVideoCompositor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVideoCompositor.swift; path = "Sources/HXPhotoPicker/Editor+View/Model/EditorVideoCompositor.swift"; sourceTree = "<group>"; };
		1667CEE1CB2D196267CD02269FDE9AA3 /* PhotoPickerListSwipeSelect.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListSwipeSelect.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerListSwipeSelect.swift; sourceTree = "<group>"; };
		1733B9CE45A9CC36C6FBFF922A0D3C8F /* EditorScaleView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorScaleView.swift; path = Sources/HXPhotoPicker/Editor/View/CropSize/EditorScaleView.swift; sourceTree = "<group>"; };
		199A0EFAEAFB5F754B4F1DD54F46A303 /* CameraViewController+Editor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CameraViewController+Editor.swift"; path = "Sources/HXPhotoPicker/Camera/Controller/CameraViewController+Editor.swift"; sourceTree = "<group>"; };
		19A0A8F9EB0E61AE1A79E72DC25B6615 /* Core+AVAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+AVAsset.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+AVAsset.swift"; sourceTree = "<group>"; };
		1B712293FCB50420C681131E67997887 /* EditorDrawTool.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorDrawTool.swift; path = "Sources/HXPhotoPicker/Editor+View/Util/EditorDrawTool.swift"; sourceTree = "<group>"; };
		1C8FABE878D0FA2D18EAB57A981E8A0A /* CameraViewController+Preview.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CameraViewController+Preview.swift"; path = "Sources/HXPhotoPicker/Camera/Controller/CameraViewController+Preview.swift"; sourceTree = "<group>"; };
		1D59F5CD82CC8D5E6AEDDB7B9FA1989E /* EditorVideoPlayerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVideoPlayerView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorVideoPlayerView.swift"; sourceTree = "<group>"; };
		1D7A1AF7E3D9B42D128F2F201E985E4D /* Core+UIView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIView.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIView.swift"; sourceTree = "<group>"; };
		1F30093DC54240D9DDE7A029AD8218FE /* Core+UIDevice.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIDevice.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIDevice.swift"; sourceTree = "<group>"; };
		1FBCD536381D27ED75B173451DAD9C44 /* EditorMusicListViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMusicListViewController.swift; path = Sources/HXPhotoPicker/Editor/Controller/EditorMusicListViewController.swift; sourceTree = "<group>"; };
		1FFE3F1461D4D7A2F119186A8080AEE5 /* PhotoPickerViewController+AlbumView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+AlbumView.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+AlbumView.swift"; sourceTree = "<group>"; };
		201F43E6FB714D2F12BB4715611219AD /* HXPhotoPicker-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "HXPhotoPicker-dummy.m"; sourceTree = "<group>"; };
		20DE3E2E1350F88459A354CCF605AFA1 /* PhotoTools.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoTools.swift; path = Sources/HXPhotoPicker/Core/Util/PhotoTools.swift; sourceTree = "<group>"; };
		21203AADFE60A2EAA646679854B350D0 /* CameraControllerProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraControllerProtocol.swift; path = Sources/HXPhotoPicker/Camera/Controller/CameraControllerProtocol.swift; sourceTree = "<group>"; };
		21584B0BEDD4E8887B70A3A2850EC6ED /* HXPhotoPicker.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = HXPhotoPicker.release.xcconfig; sourceTree = "<group>"; };
		21E63CA427D8D83D8A8CBED8F28B0BDB /* Core+UIApplication.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIApplication.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIApplication.swift"; sourceTree = "<group>"; };
		221550860117559284BE64A15F040FDF /* Core+Dictionary.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+Dictionary.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+Dictionary.swift"; sourceTree = "<group>"; };
		22A6778713FB9291D095FC6C2CF15E5D /* EditorVideoControlMaskView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVideoControlMaskView.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorVideoControlMaskView.swift; sourceTree = "<group>"; };
		22CD92FB8531CBB61F425912330F0C19 /* EditorAdjusterView+FrameView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+FrameView.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+FrameView.swift"; sourceTree = "<group>"; };
		22E9F2CFA47CE7855970CBDEB669FA97 /* AssetManager+LivePhotoURL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+LivePhotoURL.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+LivePhotoURL.swift"; sourceTree = "<group>"; };
		23468F201EEFB6C1082900082A4B9A19 /* EditorMosaicToolView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMosaicToolView.swift; path = Sources/HXPhotoPicker/Editor/View/Mosaic/EditorMosaicToolView.swift; sourceTree = "<group>"; };
		2368A59124CA95605FB5D47B60CDD613 /* PhotoHUDConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoHUDConfig.swift; path = Sources/HXPhotoPicker/Core/Config/PhotoHUDConfig.swift; sourceTree = "<group>"; };
		239166C0B73C4BB5A8DAAC41E96F066E /* PhotoFetchData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoFetchData.swift; path = Sources/HXPhotoPicker/Picker/Data/PhotoFetchData.swift; sourceTree = "<group>"; };
		23EFA3CC82F9FC4724D85FFE68B6B000 /* HXBaseViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HXBaseViewController.swift; path = Sources/HXPhotoPicker/Core/Controller/HXBaseViewController.swift; sourceTree = "<group>"; };
		243CAB4B07D2DF72404C1C9E31DB06C1 /* PhotoToolBar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoToolBar.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/ToolBar/PhotoToolBar.swift; sourceTree = "<group>"; };
		24B4FE0BE0C6D74C73084CD933B63758 /* ImageEditedResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageEditedResult.swift; path = "Sources/HXPhotoPicker/Editor+View/ImageEditedResult.swift"; sourceTree = "<group>"; };
		24DDD4A0849B06C0942057220E1E81B2 /* HXPhotoPicker.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HXPhotoPicker.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2513C6E38C06FB3EA2DA85C9C481B767 /* PhotoPickerControllerAnimator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerControllerAnimator.swift; path = Sources/HXPhotoPicker/Picker/Transition/PhotoPickerControllerAnimator.swift; sourceTree = "<group>"; };
		25B65F9DDDCDD90E43BE7797FED3D5C0 /* EditorView+GestureRecognizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+GestureRecognizer.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorView+GestureRecognizer.swift"; sourceTree = "<group>"; };
		265C677F21C6633D856E4F28AD8A1EFD /* Picker+ConfigExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+ConfigExtension.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+ConfigExtension.swift"; sourceTree = "<group>"; };
		27827C41841C74D4E098A0DF83D451CF /* PickerInteractiveTransition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerInteractiveTransition.swift; path = Sources/HXPhotoPicker/Picker/Transition/PickerInteractiveTransition.swift; sourceTree = "<group>"; };
		285F38BF41ACC86F4631E429089F69E8 /* PhotoPreviewViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoPreviewViewCell.swift; sourceTree = "<group>"; };
		2A20059888303B51E7892486BC6B003A /* EditorMaskListViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMaskListViewController.swift; path = Sources/HXPhotoPicker/Editor/Controller/EditorMaskListViewController.swift; sourceTree = "<group>"; };
		2A27B00D1A8D0609645BA96BDECDF6D0 /* PhotoAsset+Codable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Codable.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Codable.swift"; sourceTree = "<group>"; };
		2BA272F2E202985D8D03EEEEBA93BC88 /* EditorMaskListProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMaskListProtocol.swift; path = Sources/HXPhotoPicker/Editor/Protocol/EditorMaskListProtocol.swift; sourceTree = "<group>"; };
		2BCC0C5B079FF7983B6ACE826D0AB1BE /* Core+UITableView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UITableView.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UITableView.swift"; sourceTree = "<group>"; };
		2BCCA0B9085E08D1CBBA2ADDA1DEA6B1 /* PhotoPreviewListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewListView.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoPreviewListView.swift; sourceTree = "<group>"; };
		2C443D9D41A72D31A6C6C3FFE92A6C57 /* PhotoManager+Language.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoManager+Language.swift"; path = "Sources/HXPhotoPicker/Core/Util/PhotoManager+Language.swift"; sourceTree = "<group>"; };
		2C5D77296D1951C4C87F7F80C91549A1 /* JDTImageModule.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JDTImageModule.h; path = JDTImageModule/Classes/JDTImageModule.h; sourceTree = "<group>"; };
		2D25073787F2C34465FBD89072A0C8AF /* PhotoPickerFilterItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerFilterItemView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPickerFilterItemView.swift; sourceTree = "<group>"; };
		2D80F2BCBF539D3EA75EE54F08418537 /* ArrowView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ArrowView.swift; path = Sources/HXPhotoPicker/Picker/View/Kit/ArrowView.swift; sourceTree = "<group>"; };
		2DA9D39D88FA1D552DAE0658AD436F71 /* PhotoPickerBaseViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerBaseViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoPickerBaseViewCell.swift; sourceTree = "<group>"; };
		2DF4FBDBAEE565C17A3D83E5B33E95D3 /* PhotoAlbumControllerConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumControllerConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/PhotoAlbumControllerConfiguration.swift; sourceTree = "<group>"; };
		2E28BEF6DA14C2C9EF3B674F8182DFBE /* PickerManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerManager.swift; path = Sources/HXPhotoPicker/Picker/Model/PickerManager.swift; sourceTree = "<group>"; };
		2E87C5D5BA2D5A770503464702C68FE0 /* EditorAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorAsset.swift; path = Sources/HXPhotoPicker/Editor/EditorAsset.swift; sourceTree = "<group>"; };
		2F6CA6B226ECED60A2DBDCA59945C970 /* AssetPermissionsUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssetPermissionsUtil.swift; path = Sources/HXPhotoPicker/Core/Util/AssetPermissionsUtil.swift; sourceTree = "<group>"; };
		2F93BD37C3D8479C651D62C436F4981F /* PreviewPhotoViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewPhotoViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PreviewPhotoViewCell.swift; sourceTree = "<group>"; };
		300430C0A4A9689485F7ECF4239E7082 /* PhotoAlbumHeaderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumHeaderView.swift; path = Sources/HXPhotoPicker/Picker/View/Album/PhotoAlbumHeaderView.swift; sourceTree = "<group>"; };
		31416751C90BDE3CC21B1A4CDDD74C44 /* AssetManager+ImageURL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+ImageURL.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+ImageURL.swift"; sourceTree = "<group>"; };
		31B6F78E40C3969BB4BF56C347EAF9C7 /* VideoEditedResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoEditedResult.swift; path = "Sources/HXPhotoPicker/Editor+View/VideoEditedResult.swift"; sourceTree = "<group>"; };
		31DE782466BE5DE2396CBB4DA16CE0D1 /* PhotoNavigationItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoNavigationItem.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoNavigationItem.swift; sourceTree = "<group>"; };
		3266178C3986BEA7E3DE52558B843164 /* JDTImageModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = JDTImageModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		33129BB49305C38EAF061BBFF4398D46 /* IndicatorType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IndicatorType.swift; path = Sources/HXPhotoPicker/Core/Model/IndicatorType.swift; sourceTree = "<group>"; };
		3383F8E2C5FB0994A99CB3CE18A7FFB6 /* PhotoPickerController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoPickerController.swift; sourceTree = "<group>"; };
		33E74DC8F2D7C01FC765E9EBD9359F60 /* EditorBrushColorView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorBrushColorView.swift; path = Sources/HXPhotoPicker/Editor/View/Brush/EditorBrushColorView.swift; sourceTree = "<group>"; };
		3474C413E03BAB49A675FA270CA6943F /* JDTImageModule.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JDTImageModule.m; path = JDTImageModule/Classes/JDTImageModule.m; sourceTree = "<group>"; };
		354F52A0F0044AE20E410A7D676F143A /* Core+UIFont.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIFont.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIFont.swift"; sourceTree = "<group>"; };
		3668AB6CABB5FEC0ABCF6210CDD0E3C7 /* AssetManager+Image.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+Image.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+Image.swift"; sourceTree = "<group>"; };
		36A40A4132BAAA6ECA9020806AA973BF /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		3720BD45FD06C5CA7DC5A3A23265349B /* AlbumTitleViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumTitleViewConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/AlbumTitleViewConfiguration.swift; sourceTree = "<group>"; };
		374D8409ED6472B64DBB97DD92BF1BE1 /* ProgressIndefiniteView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProgressIndefiniteView.swift; path = Sources/HXPhotoPicker/Core/View/ProgressHUD/ProgressIndefiniteView.swift; sourceTree = "<group>"; };
		37B072392549BF2CAF4368B476DF02D5 /* PickerConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/PickerConfiguration.swift; sourceTree = "<group>"; };
		37F795FE155BCD35FD53D08B7A4D0D53 /* PhotoAssetCollection.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAssetCollection.swift; path = Sources/HXPhotoPicker/Picker/Model/PhotoAssetCollection.swift; sourceTree = "<group>"; };
		388B0C7C04F8004672033964FF13F190 /* PhotoPreviewListViewLayout.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewListViewLayout.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoPreviewListViewLayout.swift; sourceTree = "<group>"; };
		38931FA28AE376649ACC359D39A0FE1A /* EditorFilterEditView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorFilterEditView.swift; path = Sources/HXPhotoPicker/Editor/View/Filter/EditorFilterEditView.swift; sourceTree = "<group>"; };
		38B19E205EADA4E98976410EDF249E9A /* Core+Bundle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+Bundle.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+Bundle.swift"; sourceTree = "<group>"; };
		3B49524577C66C9983E155A59E3F32D2 /* PhotoPickerView+Preview.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+Preview.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+Preview.swift"; sourceTree = "<group>"; };
		3BBE42120AE6117418279AF9153ED501 /* PhotoPermissionPromptView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPermissionPromptView.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoPermissionPromptView.swift; sourceTree = "<group>"; };
		3BF6FFD36D652250E746F7300522BE42 /* EditorStickerTextView+CollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorStickerTextView+CollectionView.swift"; path = "Sources/HXPhotoPicker/Editor/View/Text/EditorStickerTextView+CollectionView.swift"; sourceTree = "<group>"; };
		3C432D845FDB8DD800DC0301FF20E0FE /* PhotoLoadingView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoLoadingView.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoLoadingView.swift; sourceTree = "<group>"; };
		3C4626E08C6D70CDA42B1716225D94F6 /* CameraViewController+BottomView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CameraViewController+BottomView.swift"; path = "Sources/HXPhotoPicker/Camera/Controller/CameraViewController+BottomView.swift"; sourceTree = "<group>"; };
		3C7C4B5CE5C2BD1DB973232A2C4F251C /* PhotoPickerControllerInteractiveTransition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerControllerInteractiveTransition.swift; path = Sources/HXPhotoPicker/Picker/Protocol/Transition/PhotoPickerControllerInteractiveTransition.swift; sourceTree = "<group>"; };
		******************************** /* PhotoPreviewViewController+Toolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPreviewViewController+Toolbar.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewController+Toolbar.swift"; sourceTree = "<group>"; };
		3D93E8511537AE296CC1F4787ED520D1 /* SystemCameraConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SystemCameraConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/SystemCameraConfiguration.swift; sourceTree = "<group>"; };
		3E17A40D70CC2BE7E99CEC723827E338 /* Core+LayerRTLFrame.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+LayerRTLFrame.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+LayerRTLFrame.swift"; sourceTree = "<group>"; };
		3E681373CE7E092A140F20821A4BE159 /* AssetSaveUtil.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssetSaveUtil.swift; path = Sources/HXPhotoPicker/Core/Util/AssetSaveUtil.swift; sourceTree = "<group>"; };
		3EC4A7EB7CE9B8A383291613A329FCC8 /* PhotoPickerView+Editor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+Editor.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+Editor.swift"; sourceTree = "<group>"; };
		3EE3D52CE843517C540AD7A187873AC2 /* PhotoPickerViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController.swift; sourceTree = "<group>"; };
		3F214E118F12A7332004441BAE7DF6AE /* PhotoEditorFilter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoEditorFilter.swift; path = Sources/HXPhotoPicker/Editor/Model/PhotoEditorFilter.swift; sourceTree = "<group>"; };
		40AB1A4DDA719AB7A53DE2AB85638BC2 /* EditorModels.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorModels.swift; path = "Sources/HXPhotoPicker/Editor+View/Model/EditorModels.swift"; sourceTree = "<group>"; };
		417AA4B3328803AC6CD258923E2716E7 /* EditorViewController+VideoControl.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+VideoControl.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+VideoControl.swift"; sourceTree = "<group>"; };
		418E12F5E563D168DADA19F334A7DF89 /* AssetManager+AVAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+AVAsset.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+AVAsset.swift"; sourceTree = "<group>"; };
		4350B462D8302145E8EA459D1693033D /* ProgressCircleView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProgressCircleView.swift; path = Sources/HXPhotoPicker/Core/View/ProgressHUD/ProgressCircleView.swift; sourceTree = "<group>"; };
		4405AE97A7290BF05BE6EBEDD921068B /* CameraManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraManager.swift; path = Sources/HXPhotoPicker/Camera/Controller/CameraManager.swift; sourceTree = "<group>"; };
		449F6443E35251AD6C63B02F6B0439FE /* CameraBottomView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraBottomView.swift; path = Sources/HXPhotoPicker/Camera/View/CameraBottomView.swift; sourceTree = "<group>"; };
		44A2076CF8BCEE5613EC7FE0CDF56CCB /* VideoPlaySliderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoPlaySliderView.swift; path = Sources/HXPhotoPicker/Core/View/VideoPlayer/VideoPlaySliderView.swift; sourceTree = "<group>"; };
		4825913F5CD8AB967F9B695226CDFAE4 /* Pods-ObjCSwiftDemo-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-ObjCSwiftDemo-Info.plist"; sourceTree = "<group>"; };
		492F778245EEAA2DD7580A4CA75F2D47 /* SwiftPhotoAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SwiftPhotoAsset.swift; sourceTree = "<group>"; };
		4A169FA30F6F6FCCB8017CE00D7336EF /* AlbumSectionHeaderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumSectionHeaderView.swift; path = Sources/HXPhotoPicker/Picker/View/Album/AlbumSectionHeaderView.swift; sourceTree = "<group>"; };
		4B1FCD59C785A158552AE6C816877FAC /* PhotoBrowserInteractiveTransition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoBrowserInteractiveTransition.swift; path = Sources/HXPhotoPicker/Picker/Protocol/Transition/PhotoBrowserInteractiveTransition.swift; sourceTree = "<group>"; };
		4BCC1E9AB6067A64C54EA5A0166CFB42 /* DeviceOrientationHelper.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DeviceOrientationHelper.swift; path = Sources/HXPhotoPicker/Camera/Model/DeviceOrientationHelper.swift; sourceTree = "<group>"; };
		4D06151F50611D629C971B8BC33B3808 /* PhotoPickerViewController+Camera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+Camera.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+Camera.swift"; sourceTree = "<group>"; };
		4D064ADDE951DF2CCACF53E53E87982C /* LocalAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LocalAsset.swift; path = Sources/HXPhotoPicker/Picker/Model/LocalAsset/LocalAsset.swift; sourceTree = "<group>"; };
		4EA7303E022EA3333F6C26C391E8B29F /* PhotoAlbumController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumController.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/AlbumList/PhotoAlbumController.swift; sourceTree = "<group>"; };
		4FB35256EFE59BCC86317CC292956190 /* PhotoImageCancelItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoImageCancelItemView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoImageCancelItemView.swift; sourceTree = "<group>"; };
		4FD1C4E06593919519B1D9F580D76EF4 /* Picker+PhotoManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+PhotoManager.swift"; path = "Sources/HXPhotoPicker/Picker/Util/Picker+PhotoManager.swift"; sourceTree = "<group>"; };
		50732B5E3676C444E0BEBA6275E6170A /* EditorViewController+UINavigationController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+UINavigationController.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+UINavigationController.swift"; sourceTree = "<group>"; };
		5234AB30D6238048D373CE7864BF6AEB /* ResourceBundle-HXPhotoPicker_Privacy-HXPhotoPicker-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-HXPhotoPicker_Privacy-HXPhotoPicker-Info.plist"; sourceTree = "<group>"; };
		52B7E9208D24DE8C9A2C2749922B9D78 /* PhotoPreviewContentPhotoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewContentPhotoView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPreviewContentPhotoView.swift; sourceTree = "<group>"; };
		5347F0C6F4372A43FFE10B4E243710A4 /* HXPhotoPicker-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "HXPhotoPicker-prefix.pch"; sourceTree = "<group>"; };
		536BA9E2E17F41F53A01D6CB8C28DA14 /* EditorViewController+Filters.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Filters.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Filters.swift"; sourceTree = "<group>"; };
		53CB4DA0AEAC9C4E53EB1A56095D9C60 /* PhotoPanGestureRecognizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPanGestureRecognizer.swift; path = Sources/HXPhotoPicker/Core/Model/PhotoPanGestureRecognizer.swift; sourceTree = "<group>"; };
		5422CA8608B2FA688A7D7D644AB745E6 /* ProgressImageView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProgressImageView.swift; path = Sources/HXPhotoPicker/Core/View/ProgressHUD/ProgressImageView.swift; sourceTree = "<group>"; };
		543EABD5688C99ED261487FEA57C272C /* EditorViewProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorViewProtocol.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorViewProtocol.swift"; sourceTree = "<group>"; };
		544413451FE214084FFE9FEC3F1ECE34 /* PhotoPickerDataStatus.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerDataStatus.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoPickerDataStatus.swift; sourceTree = "<group>"; };
		558E84A3D3BB9CCDFA22EFE5D4472960 /* EditorRatioToolViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorRatioToolViewCell.swift; path = Sources/HXPhotoPicker/Editor/View/CropSize/EditorRatioToolViewCell.swift; sourceTree = "<group>"; };
		56048137FCB6FC7A1596045363BCD503 /* Picker+LivePhotoTools.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+LivePhotoTools.swift"; path = "Sources/HXPhotoPicker/Picker/Util/Picker+LivePhotoTools.swift"; sourceTree = "<group>"; };
		561B5B856F55D2AB6B4B1CD77E1E19BE /* PhotoFetchAssetCollection.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoFetchAssetCollection.swift; path = Sources/HXPhotoPicker/Picker/Protocol/Fetch/PhotoFetchAssetCollection.swift; sourceTree = "<group>"; };
		56E3CB033EDC9733A7E9B4066DFCBA40 /* PhotoPickerLimitCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerLimitCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoPickerLimitCell.swift; sourceTree = "<group>"; };
		570A91174D6C4846D15DC63C0184C1C5 /* PhotoPickerController+PHPhotoLibrary.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerController+PHPhotoLibrary.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoPickerController+PHPhotoLibrary.swift"; sourceTree = "<group>"; };
		574A182DB58FE7697656B8FAB2DB50BB /* NetworkAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NetworkAsset.swift; path = Sources/HXPhotoPicker/Picker/Model/LocalAsset/NetworkAsset.swift; sourceTree = "<group>"; };
		58A6C3C54FBCE42AC4591797FDBAE095 /* PhotoPickerControllerInteractiveAnimator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerControllerInteractiveAnimator.swift; path = Sources/HXPhotoPicker/Picker/Transition/PhotoPickerControllerInteractiveAnimator.swift; sourceTree = "<group>"; };
		59192AD9A197F2CBF49D979E61BBCBCF /* Picker+PhotoTools.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+PhotoTools.swift"; path = "Sources/HXPhotoPicker/Picker/Util/Picker+PhotoTools.swift"; sourceTree = "<group>"; };
		597F9F432888B2C069145B01FDB8FC41 /* EditorMusicView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMusicView.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorMusicView.swift; sourceTree = "<group>"; };
		5A486608DC3B74B458F3C4B6E599F6F8 /* EditorChartletViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorChartletViewCell.swift; path = Sources/HXPhotoPicker/Editor/View/Chartlet/EditorChartletViewCell.swift; sourceTree = "<group>"; };
		5A81FBE6335FD8A4A976CCD9FD672BC8 /* EmptyView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EmptyView.swift; path = Sources/HXPhotoPicker/Picker/View/Kit/EmptyView.swift; sourceTree = "<group>"; };
		5A9B2FC917F92187A663B16A27348A57 /* Core+URL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+URL.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+URL.swift"; sourceTree = "<group>"; };
		5AD5023CDB2089BBD3F7CEBB26C4B057 /* EditorVideoControlView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVideoControlView.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorVideoControlView.swift; sourceTree = "<group>"; };
		5B1250D07BCCD70BD33720C6E4FFE6B9 /* EditorViewController+Brush.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Brush.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Brush.swift"; sourceTree = "<group>"; };
		5B278228584CE2103FF6C1F876E4B3E1 /* PhotoPickerListCollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListCollectionView.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerListCollectionView.swift; sourceTree = "<group>"; };
		5B67425251B51353AE8A7D4A38019ED0 /* DeniedAuthorizationView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DeniedAuthorizationView.swift; path = Sources/HXPhotoPicker/Picker/View/DeniedAuthorizationView.swift; sourceTree = "<group>"; };
		5BF8B40CF7294D1A6B830858A212D16F /* PhotoPickerListViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerListViewController.swift; sourceTree = "<group>"; };
		5C4F0A230FC76EEAAB91AA8CFAFEE25A /* PhotoPickerViewProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerViewProtocol.swift; path = Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerViewProtocol.swift; sourceTree = "<group>"; };
		5CACAFC8C7EA8FF23B8984FD77077C8B /* HXPhotoPicker.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = HXPhotoPicker.modulemap; sourceTree = "<group>"; };
		5EDDD0D27D73280213454C9A65E4F643 /* Core+UIViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIViewController.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIViewController.swift"; sourceTree = "<group>"; };
		60F38BD222663EDB7D58414086C7FF80 /* AssetError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssetError.swift; path = Sources/HXPhotoPicker/Picker/Model/AssetError.swift; sourceTree = "<group>"; };
		6151F9B26EA26FD1DAF8AE03B74B9D97 /* Pods-ObjCSwiftDemo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ObjCSwiftDemo.debug.xcconfig"; sourceTree = "<group>"; };
		626709B25F4E69020AC52FFEEF2B729A /* SwiftPickerResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SwiftPickerResult.swift; sourceTree = "<group>"; };
		63A0C8A37013E36CAE04B87E2D50A6F2 /* Core+UIImage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIImage.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIImage.swift"; sourceTree = "<group>"; };
		640168DE94AFC74AA086F0F2C9D604EF /* EditorStickerTextView+Delegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorStickerTextView+Delegate.swift"; path = "Sources/HXPhotoPicker/Editor/View/Text/EditorStickerTextView+Delegate.swift"; sourceTree = "<group>"; };
		64CAB42769526B703114F62BA54294B0 /* PhotoPreviewViewController+CollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPreviewViewController+CollectionView.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewController+CollectionView.swift"; sourceTree = "<group>"; };
		656BC291DE4861E74F84D666CB0F469B /* PreviewVideoViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewVideoViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PreviewVideoViewCell.swift; sourceTree = "<group>"; };
		65A82F18C2977BBCF29FF2583045734A /* EditorView+ScrollView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+ScrollView.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorView+ScrollView.swift"; sourceTree = "<group>"; };
		662FC3274A5CFCC38963FEE272890857 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = Sources/HXPhotoPicker/Resources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		664F4237C8BE4A9420895261099D34DA /* Pods-ObjCSwiftDemo-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-ObjCSwiftDemo-acknowledgements.markdown"; sourceTree = "<group>"; };
		681EDFAA6E48BA557A3C85A1C4F0228F /* EditorFrameView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorFrameView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorFrameView.swift"; sourceTree = "<group>"; };
		684378CFCE2AD25B8D014F502640003B /* Core+UIBarButtonItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIBarButtonItem.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIBarButtonItem.swift"; sourceTree = "<group>"; };
		68D33C4364FB684AFD506497AA7BB264 /* EditorVideoTool.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVideoTool.swift; path = "Sources/HXPhotoPicker/Editor+View/Util/EditorVideoTool.swift"; sourceTree = "<group>"; };
		69047A871878B25CFA264C7DB91FF159 /* CaptureVideoPreviewView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaptureVideoPreviewView.swift; path = Sources/HXPhotoPicker/Picker/View/CaptureVideoPreviewView.swift; sourceTree = "<group>"; };
		6955F08988CC12E37BD1F81EEC8989F0 /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework; sourceTree = DEVELOPER_DIR; };
		6A35E34E546734B7D01B703E5B888E06 /* EditorView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorView.swift"; sourceTree = "<group>"; };
		6A9048CFCB871D7170D4EEB7A847CB0C /* CameraViewController+Location.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CameraViewController+Location.swift"; path = "Sources/HXPhotoPicker/Camera+Location/CameraViewController+Location.swift"; sourceTree = "<group>"; };
		6BE91D8479997127D6409DC89B96933D /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		6C0DF4B5D571D1B934FA77506277EE0A /* PhotoPickerView+Asset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+Asset.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+Asset.swift"; sourceTree = "<group>"; };
		6ED0C3B1CB142719C5CA1CB3FE4762AE /* HXPhotoPicker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HXPhotoPicker.swift; path = Sources/HXPhotoPicker/Core/HXPhotoPicker.swift; sourceTree = "<group>"; };
		6F0A5625720E3A09F21AD09CD9B6D696 /* PhotoMyAlbumViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoMyAlbumViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Album/PhotoMyAlbumViewController.swift; sourceTree = "<group>"; };
		6F1EDAEE7459431DE287042C93B2FCD9 /* Editor+PhotoTools.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Editor+PhotoTools.swift"; path = "Sources/HXPhotoPicker/Editor/Util/Editor+PhotoTools.swift"; sourceTree = "<group>"; };
		70530C3EEB787DC2042A8C2C771C312D /* PhotoPickerBottomNumberView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerBottomNumberView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPickerBottomNumberView.swift; sourceTree = "<group>"; };
		709B4104624C830A8111F35B2E3635CA /* Picker+PhotoAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+PhotoAsset.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+PhotoAsset.swift"; sourceTree = "<group>"; };
		70E207315CC6F2FE9AB3FD99F0B5D602 /* EditorStickerTextViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorStickerTextViewController.swift; path = Sources/HXPhotoPicker/Editor/Controller/EditorStickerTextViewController.swift; sourceTree = "<group>"; };
		710B03E0738E38F4038E5859A93B18B4 /* Picker+UIImageView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+UIImageView.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+UIImageView.swift"; sourceTree = "<group>"; };
		72208964807741AD183F3E36D83A90A7 /* EditorAdjusterView+Rotate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+Rotate.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+Rotate.swift"; sourceTree = "<group>"; };
		745BDB2F10187F02CE1A4F6111908D43 /* Picker+Int.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+Int.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+Int.swift"; sourceTree = "<group>"; };
		74E7EB7A97AE8D6A4851E9E76EE5DC8F /* PhotoPickerNavigationTitle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerNavigationTitle.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerNavigationTitle.swift; sourceTree = "<group>"; };
		75048AB2F7832942E5A02A116732FD0C /* PreviewMetalView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewMetalView.swift; path = Sources/HXPhotoPicker/Camera/View/PreviewMetalView.swift; sourceTree = "<group>"; };
		76E4DCB71CDB143EAB86CFB104768646 /* Pods-ObjCSwiftDemo.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-ObjCSwiftDemo.modulemap"; sourceTree = "<group>"; };
		******************************** /* SelectBoxView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SelectBoxView.swift; path = Sources/HXPhotoPicker/Core/View/SelectBoxView.swift; sourceTree = "<group>"; };
		7771F644EC541C4B1928477071709DC9 /* PhotoPreviewViewController+SelectBox.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPreviewViewController+SelectBox.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewController+SelectBox.swift"; sourceTree = "<group>"; };
		******************************** /* PhotoPreviewViewControllerProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewViewControllerProtocol.swift; path = Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewControllerProtocol.swift; sourceTree = "<group>"; };
		7984854A9E6B4963A93F19EBC7B68D5C /* Core+FileManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+FileManager.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+FileManager.swift"; sourceTree = "<group>"; };
		7A3D1C145E71E4F182E42119EAD815AE /* Core+UIViewRTLFrame.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIViewRTLFrame.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIViewRTLFrame.swift"; sourceTree = "<group>"; };
		7A7EC1CE5A1231D6A9C716A27FB4B7CD /* PhotoPickerView+CollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+CollectionView.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+CollectionView.swift"; sourceTree = "<group>"; };
		7ADA493E783217B491C3B3F97D4A417C /* EditorStickersItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorStickersItemView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorStickersItemView.swift"; sourceTree = "<group>"; };
		7B8095CE0780D4129706728A8452A778 /* EditorDrawView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorDrawView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorDrawView.swift"; sourceTree = "<group>"; };
		7BFD29E391E22EE680A7699DA35A0B43 /* PhotoPreviewContentViewProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewContentViewProtocol.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPreviewContentViewProtocol.swift; sourceTree = "<group>"; };
		7D1A2DCD5A6F0435D9169278412A2AC0 /* PhotoPreviewViewController+Editor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPreviewViewController+Editor.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewController+Editor.swift"; sourceTree = "<group>"; };
		7D4CE2129947066D5EE5C99EB05F726C /* EditorAdjusterView+Video.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+Video.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+Video.swift"; sourceTree = "<group>"; };
		7DEFE13B97AFC1F24F4FC06783E7C986 /* PhotoPickerSwitchLayout.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerSwitchLayout.swift; path = Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerSwitchLayout.swift; sourceTree = "<group>"; };
		7E23569E056ABA66F7622E1289D28A74 /* EditorViewController+Text.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Text.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Text.swift"; sourceTree = "<group>"; };
		7F251867819303E98BCB2ACAC5669D20 /* PhotoPickerView+Function.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+Function.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+Function.swift"; sourceTree = "<group>"; };
		7FA386A2CAFDE68CBA7593DD43D27286 /* AssetManager+VideoURL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+VideoURL.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+VideoURL.swift"; sourceTree = "<group>"; };
		7FF715A105B83DCE65C4A4B111DF8F1C /* VideoEditorMusic.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoEditorMusic.swift; path = Sources/HXPhotoPicker/Editor/Model/VideoEditorMusic.swift; sourceTree = "<group>"; };
		807C4D50EA3E351ECA6D86909712769C /* AppearanceStyle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppearanceStyle.swift; path = Sources/HXPhotoPicker/Core/Model/AppearanceStyle.swift; sourceTree = "<group>"; };
		80D6AA4452E70310081F4712F4BFA513 /* EditorView+CIImage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+CIImage.swift"; path = "Sources/HXPhotoPicker/Editor+View/Extension/EditorView+CIImage.swift"; sourceTree = "<group>"; };
		80E9543D306FF7D79E78EAF673F58FF7 /* Picker+PHAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+PHAsset.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+PHAsset.swift"; sourceTree = "<group>"; };
		81913EF7492B4077BB4E51D74E6D0320 /* EditorFiltersView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorFiltersView.swift; path = Sources/HXPhotoPicker/Editor/View/Filter/EditorFiltersView.swift; sourceTree = "<group>"; };
		8222B901A5D698452D79E5B5956FFC34 /* Core+UIImageView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIImageView.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIImageView.swift"; sourceTree = "<group>"; };
		82A9736DFF9D1A07390287D5FD1D3978 /* JDTImageModule-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "JDTImageModule-Info.plist"; sourceTree = "<group>"; };
		841297B6767DB799B3FE634A2860CCAB /* EditorConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorConfiguration.swift; path = Sources/HXPhotoPicker/Editor/Config/EditorConfiguration.swift; sourceTree = "<group>"; };
		8517B133E25CDB1B7DF00FA567D5D57A /* PhotoPreviewVideoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewVideoView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPreviewVideoView.swift; sourceTree = "<group>"; };
		8541F4B9A66C5874778B820DD6761138 /* PickerBottomViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerBottomViewConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/PickerBottomViewConfiguration.swift; sourceTree = "<group>"; };
		86EA281DBA8E46CACFBC2D77FBEEDA60 /* EditorMosaicView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMosaicView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorMosaicView.swift"; sourceTree = "<group>"; };
		87426884B220B437C2317FA73CF0B5E9 /* PhotoPickerViewController+Toolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+Toolbar.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+Toolbar.swift"; sourceTree = "<group>"; };
		881094C4979E410E6AF6E86318DA4F98 /* JDTImageModule.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = JDTImageModule.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		887E8C30595E12D37E1C57819C00A1EC /* PhotoThumbnailView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoThumbnailView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoThumbnailView.swift; sourceTree = "<group>"; };
		891154F04051668B1F84BE657A8DC04B /* ProgressHUD.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProgressHUD.swift; path = Sources/HXPhotoPicker/Core/View/ProgressHUD/ProgressHUD.swift; sourceTree = "<group>"; };
		8926F9A654DB10F3FDFABCA3C68064A3 /* HXPhotoPicker-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "HXPhotoPicker-umbrella.h"; sourceTree = "<group>"; };
		89C068E6FDF9FF0EE81C869EF03FC1F0 /* CameraController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraController.swift; path = Sources/HXPhotoPicker/Camera/Controller/CameraController.swift; sourceTree = "<group>"; };
		8AF58A82392843352FC90EF232DD7568 /* EditorAdjusterView+ScrollView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+ScrollView.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+ScrollView.swift"; sourceTree = "<group>"; };
		8B0E9EEF82C6FE9A3E173E6C2879D0AA /* ExportPreset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExportPreset.swift; path = Sources/HXPhotoPicker/Core/Model/ExportPreset.swift; sourceTree = "<group>"; };
		8B8CF63B8795710CACC35F1BB469DD99 /* SwiftPickerConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SwiftPickerConfiguration.swift; sourceTree = "<group>"; };
		8BFB97059FCFEA3D48BA32FE9D6A616C /* EditorChartlet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorChartlet.swift; path = Sources/HXPhotoPicker/Editor/Model/EditorChartlet.swift; sourceTree = "<group>"; };
		8C5519BCAE6833CFDC4A2F949BE4A61F /* Pods-ObjCSwiftDemo-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-ObjCSwiftDemo-frameworks.sh"; sourceTree = "<group>"; };
		8C8506A89DE0C7792CCF6A4B4934690A /* PhotoPickerViewController+FetchAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+FetchAsset.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+FetchAsset.swift"; sourceTree = "<group>"; };
		8CA16080C639896A2CD61AC4F98F8343 /* Picker+PHAssetCollection.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+PHAssetCollection.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+PHAssetCollection.swift"; sourceTree = "<group>"; };
		8D002CB238D49910630F3ADAFD15AFB0 /* PhotoPickerListAssets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListAssets.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerListAssets.swift; sourceTree = "<group>"; };
		8D0285F28163890D20C1C5FAA3E5E506 /* PhotoSplitViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoSplitViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoSplitViewController.swift; sourceTree = "<group>"; };
		8D400C8DE7A3AA4A130C23BD14B828E9 /* EditorStickersTrashView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorStickersTrashView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorStickersTrashView.swift"; sourceTree = "<group>"; };
		8D59106DE26A2FDE8E2B0BCFFBF2388F /* JDTImageModule-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JDTImageModule-umbrella.h"; sourceTree = "<group>"; };
		8D5B847B39B717ADFF6273BE06162347 /* PhotoHUDProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoHUDProtocol.swift; path = Sources/HXPhotoPicker/Core/Config/PhotoHUDProtocol.swift; sourceTree = "<group>"; };
		8E71B2434C727860F1D0634C3C22CD1C /* PhotoAsset+URL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+URL.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+URL.swift"; sourceTree = "<group>"; };
		8FB3244CF8960971D9602C2587E1BDC2 /* PhotoAsset+Image.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Image.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Image.swift"; sourceTree = "<group>"; };
		8FC093A59283E28FE8F6F342859945C9 /* PhotoAlbumViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/Album/PhotoAlbumViewCell.swift; sourceTree = "<group>"; };
		8FD9805A29369B3863D67F309EDA5418 /* ExpandButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpandButton.swift; path = Sources/HXPhotoPicker/Editor/View/ExpandButton.swift; sourceTree = "<group>"; };
		901B9CB29917DA424A370715B31F2D18 /* JDTImageModule.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = JDTImageModule.modulemap; sourceTree = "<group>"; };
		90E70CD912661F1824EF4ABC6B1A2682 /* Core+UICollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UICollectionView.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UICollectionView.swift"; sourceTree = "<group>"; };
		90E82FF5E117D9E926606C741F0D77B3 /* EditorControlView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorControlView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorControlView.swift"; sourceTree = "<group>"; };
		91756DAFF4884D2D3E0C9BAA43248DAF /* PhotoAlbumCollectionViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumCollectionViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/Album/PhotoAlbumCollectionViewCell.swift; sourceTree = "<group>"; };
		92D70113DE6E938C6208B194A4DF7FF1 /* CameraRenderer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraRenderer.swift; path = Sources/HXPhotoPicker/Camera/Model/CameraRenderer.swift; sourceTree = "<group>"; };
		931D28714877B0595FAFF2988CAD21D1 /* EditorAdjusterView+ContentView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+ContentView.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+ContentView.swift"; sourceTree = "<group>"; };
		93F381FD5C99DE9D1F78DED92F32E1A8 /* PhotoPickerWeChatViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerWeChatViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoPickerWeChatViewCell.swift; sourceTree = "<group>"; };
		95F12217D5E6CDEE2AECA748ABA136E6 /* PhotoBrowserAnimator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoBrowserAnimator.swift; path = Sources/HXPhotoPicker/Picker/Transition/PhotoBrowserAnimator.swift; sourceTree = "<group>"; };
		96A74C56499F32B1BA5EFD69F80379EB /* EditorViewController+Processing.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Processing.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Processing.swift"; sourceTree = "<group>"; };
		978829349A370A8F5C784CB41ACDC16D /* HXImageViewProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HXImageViewProtocol.swift; path = Sources/HXPhotoPicker/Core/Config/HXImageViewProtocol.swift; sourceTree = "<group>"; };
		9858C8CB63795012D11DB2823D26F624 /* Core+UILabel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UILabel.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UILabel.swift"; sourceTree = "<group>"; };
		99012C6177330A6C13868B40DFF3769C /* PhotoPickerControllerFectch.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerControllerFectch.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoPickerControllerFectch.swift; sourceTree = "<group>"; };
		991EB681C7EC3345C2100833B424F65C /* PhotoPreviewViewController+NavigationController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPreviewViewController+NavigationController.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewController+NavigationController.swift"; sourceTree = "<group>"; };
		9BD9A8A607EF9971FC634DB1B34D6D89 /* PhotoPickerData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerData.swift; path = Sources/HXPhotoPicker/Picker/Data/PhotoPickerData.swift; sourceTree = "<group>"; };
		9BE530E5EFC06D9B34EE179F2FE0DA36 /* PhotoPeekViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPeekViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPeekViewController.swift; sourceTree = "<group>"; };
		9C87B621086B1C54011333599EFF2723 /* EditorAudioAnimationView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorAudioAnimationView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAudioAnimationView.swift"; sourceTree = "<group>"; };
		9CC9CB4577D12D6D1A7DECEA072D9422 /* EditorView+UIView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+UIView.swift"; path = "Sources/HXPhotoPicker/Editor+View/Extension/EditorView+UIView.swift"; sourceTree = "<group>"; };
		9D0DB1AA5EFAAE3CDE271745B07B02EB /* CustomLanguage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CustomLanguage.swift; path = Sources/HXPhotoPicker/Core/Model/CustomLanguage.swift; sourceTree = "<group>"; };
		9D1ADF73EAB464C50E9A5AFEAF8493AE /* EditorViewController+ToolsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+ToolsView.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+ToolsView.swift"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9DA28EC100612F848DA85881AC52C0C0 /* PhotoPickerViewController+Editor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+Editor.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+Editor.swift"; sourceTree = "<group>"; };
		9DCB0968DF5093644B0644294F4E27A9 /* EditorChartletViewListCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorChartletViewListCell.swift; path = Sources/HXPhotoPicker/Editor/View/Chartlet/EditorChartletViewListCell.swift; sourceTree = "<group>"; };
		9F5F68C9F086246EE2B80A1780765C75 /* AssetURLResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssetURLResult.swift; path = Sources/HXPhotoPicker/Picker/AssetURLResult.swift; sourceTree = "<group>"; };
		9FAA07E956B3AD5059A8B4053922E36A /* NotAuthorizedConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NotAuthorizedConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/NotAuthorizedConfiguration.swift; sourceTree = "<group>"; };
		A1BF7C2A5D4AC66058E52AB8F224E69D /* EditorVideoControlViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVideoControlViewCell.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorVideoControlViewCell.swift; sourceTree = "<group>"; };
		A1E58FD5F48743D565FE2B3158DAD4B9 /* JDTImageModule.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; path = JDTImageModule.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A2D446A23DD905251DD8B14D443EE9D1 /* CameraResultViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraResultViewController.swift; path = Sources/HXPhotoPicker/Camera/Controller/CameraResultViewController.swift; sourceTree = "<group>"; };
		A3143AC72F045E962D820FECD4E6A918 /* PhotoBrowserInteractiveAnimator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoBrowserInteractiveAnimator.swift; path = Sources/HXPhotoPicker/Picker/Transition/PhotoBrowserInteractiveAnimator.swift; sourceTree = "<group>"; };
		A31D7F0AD3CD39B734A09530C42A6005 /* EditorView+UIImage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+UIImage.swift"; path = "Sources/HXPhotoPicker/Editor+View/Extension/EditorView+UIImage.swift"; sourceTree = "<group>"; };
		A3620F1826196D3CF6A3C5E46799F5A5 /* README.md */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		A381EBE707A2E2E5D34AF239D383538D /* EditorTypes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorTypes.swift; path = "Sources/HXPhotoPicker/Editor+View/Model/EditorTypes.swift"; sourceTree = "<group>"; };
		A3B616E9E3990DEF8AD9C998E20ACD70 /* PhotoPreviewSelectedViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewSelectedViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoPreviewSelectedViewCell.swift; sourceTree = "<group>"; };
		A3F574521FD9FE506349F7BD4C56E601 /* EditorAdjusterView+Mirror.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+Mirror.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+Mirror.swift"; sourceTree = "<group>"; };
		A4DAECBC434D0EEB28E1B2D18954AD67 /* LivePhotoError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LivePhotoError.swift; path = Sources/HXPhotoPicker/Picker/Model/LivePhotoError.swift; sourceTree = "<group>"; };
		A54D7406174AC4D0CE3EFDE0B53751EF /* AssetManager+AVAssetExportSession.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+AVAssetExportSession.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+AVAssetExportSession.swift"; sourceTree = "<group>"; };
		A61E287ACF3EC037C9C36340833BCA4D /* EditorFrameView+VideoPlay.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorFrameView+VideoPlay.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorFrameView+VideoPlay.swift"; sourceTree = "<group>"; };
		A7666775D8F08F87AE8FB5215AC54E79 /* PhotoPickerSelectableViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerSelectableViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoPickerSelectableViewCell.swift; sourceTree = "<group>"; };
		A8A6A747A1356D5A504D90EC7A4A9C0D /* Camera+PhotoTools.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Camera+PhotoTools.swift"; path = "Sources/HXPhotoPicker/Camera/Util/Camera+PhotoTools.swift"; sourceTree = "<group>"; };
		A9E7BD62C42F903F2359F1738E782E76 /* ImageContentType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageContentType.swift; path = Sources/HXPhotoPicker/Core/Model/ImageContentType.swift; sourceTree = "<group>"; };
		AA86496717AD4C66D9FADDC12E0E8763 /* ImageViewConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageViewConfig.swift; path = Sources/HXPhotoPicker/Core/Config/ImageViewConfig.swift; sourceTree = "<group>"; };
		AB5D5527F2F44719CC3E71843A22A15B /* EditorView+AdjusterView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+AdjusterView.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorView+AdjusterView.swift"; sourceTree = "<group>"; };
		AB77DD56A5062F7655E8C4E992737F63 /* EditorView+CGFloat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+CGFloat.swift"; path = "Sources/HXPhotoPicker/Editor+View/Extension/EditorView+CGFloat.swift"; sourceTree = "<group>"; };
		ABF3D7D7A51DF564BC0A442577BF519F /* PhotoBrowserAnimationTransitioning.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoBrowserAnimationTransitioning.swift; path = Sources/HXPhotoPicker/Picker/Protocol/Transition/PhotoBrowserAnimationTransitioning.swift; sourceTree = "<group>"; };
		AC2BB04A00E5980D08522443BE270631 /* PlayButton.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PlayButton.swift; path = Sources/HXPhotoPicker/Core/View/VideoPlayer/PlayButton.swift; sourceTree = "<group>"; };
		AC939ECBBB358DFEB87C5A343C18A949 /* Core+Data.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+Data.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+Data.swift"; sourceTree = "<group>"; };
		ACB89A1E18FFC9DA950A69DB608CE435 /* PhotoTools+File.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoTools+File.swift"; path = "Sources/HXPhotoPicker/Core/Util/PhotoTools+File.swift"; sourceTree = "<group>"; };
		ACBF2803ADC011D484A1A508DB226C45 /* PhotoPickerController+Transitioning.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerController+Transitioning.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoPickerController+Transitioning.swift"; sourceTree = "<group>"; };
		AD35F273D9746B18C4BB6E036FBF455C /* PhotoPickerFilterViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerFilterViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerFilterViewController.swift; sourceTree = "<group>"; };
		ADA0AE8C603E25DB2343D5C9900A4D1B /* LanguageType.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LanguageType.swift; path = Sources/HXPhotoPicker/Core/Model/LanguageType.swift; sourceTree = "<group>"; };
		ADA96359B286FFEEDE25CDA2AAD9020A /* TickView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TickView.swift; path = Sources/HXPhotoPicker/Picker/View/Kit/TickView.swift; sourceTree = "<group>"; };
		AE10BE8BC584F1EA0A3AC092B6CED14C /* ImageResource.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ImageResource.swift; path = Sources/HXPhotoPicker/Core/Util/ImageResource.swift; sourceTree = "<group>"; };
		AE1A52094CF232757E37725F426CEE59 /* CameraFilter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraFilter.swift; path = Sources/HXPhotoPicker/Camera/Model/CameraFilter.swift; sourceTree = "<group>"; };
		AE2DA113C179A1212BD24A28C3BE336F /* EditorFilterParameterView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorFilterParameterView.swift; path = Sources/HXPhotoPicker/Editor/View/Filter/EditorFilterParameterView.swift; sourceTree = "<group>"; };
		AE8B055FA8006491BCABC86DC2D0EA75 /* EditorAdjusterViewProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorAdjusterViewProtocol.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterViewProtocol.swift"; sourceTree = "<group>"; };
		AFF094815CCE529B5DCE3302D31E87E0 /* ProgressCricleJoinView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProgressCricleJoinView.swift; path = Sources/HXPhotoPicker/Core/View/ProgressHUD/ProgressCricleJoinView.swift; sourceTree = "<group>"; };
		B028000BCCDD071B781E2250027AE9A8 /* PhotoPickerListFectchCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListFectchCell.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerListFectchCell.swift; sourceTree = "<group>"; };
		B269ACD9FC933988CDB3751B959D0D61 /* PhotoPreviewListViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewListViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoPreviewListViewCell.swift; sourceTree = "<group>"; };
		B286B37B58042187E66234C7AC79EB18 /* CameraConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraConfiguration.swift; path = Sources/HXPhotoPicker/Camera/Config/CameraConfiguration.swift; sourceTree = "<group>"; };
		B34EDEE6495B2C5E198EE324134DBAED /* EditorViewController+Chartlet.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Chartlet.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Chartlet.swift"; sourceTree = "<group>"; };
		B35AC25D27FECE53721F41B14ADBEB6C /* EditorChartletListProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorChartletListProtocol.swift; path = Sources/HXPhotoPicker/Editor/Protocol/EditorChartletListProtocol.swift; sourceTree = "<group>"; };
		B41DF9BB358A82B74EE3BE34D88E27FE /* PhotoAsset+Equatable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Equatable.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Equatable.swift"; sourceTree = "<group>"; };
		B4EC2827755DE52F18E04B210B41E1C4 /* JDTImageModule-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "JDTImageModule-dummy.m"; sourceTree = "<group>"; };
		B51817FD2FEB3E43633075C3EF92BF88 /* Pods-ObjCSwiftDemo-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-ObjCSwiftDemo-acknowledgements.plist"; sourceTree = "<group>"; };
		B51CCD9558282BD278E2FE04C5E5D55C /* AssetManager+ImageData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+ImageData.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+ImageData.swift"; sourceTree = "<group>"; };
		B6121E4C9EE4DAB7308F516C45E8E22E /* PhotoPickerPageViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerPageViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerPageViewController.swift; sourceTree = "<group>"; };
		B6696A56ACAAD989023AEBB20B528DD8 /* PreviewLivePhotoViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewLivePhotoViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PreviewLivePhotoViewCell.swift; sourceTree = "<group>"; };
		B6A24E16A6DD4F0CFFEFD938B47114A7 /* SwiftAssetURLResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SwiftAssetURLResult.swift; sourceTree = "<group>"; };
		B6AF672E111C559E9FA004AB385D877C /* PhotoAsset+Network.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Network.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Network.swift"; sourceTree = "<group>"; };
		B6F505333A0F849FEEECA136226F47AE /* PhotoPreviewContentLivePhotoView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewContentLivePhotoView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPreviewContentLivePhotoView.swift; sourceTree = "<group>"; };
		B95858DD73C99FD8E75B920B2FA910E2 /* PickerResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerResult.swift; path = Sources/HXPhotoPicker/Picker/PickerResult.swift; sourceTree = "<group>"; };
		B98857619009286532DF174050ED3973 /* Core+CGFloat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+CGFloat.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+CGFloat.swift"; sourceTree = "<group>"; };
		B9CAC87B32E89186965BB55B63A5E28E /* PhotoAsset+Request.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Request.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Request.swift"; sourceTree = "<group>"; };
		BB2E9327281C7E6ADAAFA50519FBD70A /* EditorViewController+Ratio.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Ratio.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Ratio.swift"; sourceTree = "<group>"; };
		BB4D8A08D155332EA4B9A3B8721804E8 /* PhotoAsset+FileSize.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+FileSize.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+FileSize.swift"; sourceTree = "<group>"; };
		BC71C725F1D8D2CC11EE2C719F7D0439 /* PickerCameraViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerCameraViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PickerCameraViewCell.swift; sourceTree = "<group>"; };
		BD5968928B9F0760538F06AD6D8AC691 /* EditorAdjusterView+ScreenRotation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+ScreenRotation.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+ScreenRotation.swift"; sourceTree = "<group>"; };
		BD7E8C4AACC66454A525FB1243A9325B /* PhotoPickerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerView.swift; path = Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView.swift; sourceTree = "<group>"; };
		BD8619831D4C378D268F23BD9140F6ED /* AlbumListConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumListConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/AlbumListConfiguration.swift; sourceTree = "<group>"; };
		BDE9435DDA3E22DE1F324BC6E9FB97D7 /* SystemCameraViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SystemCameraViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Picker/SystemCameraViewController.swift; sourceTree = "<group>"; };
		BE1069D2AE707CDAE357CAA9F6A39BFA /* EditorRatioToolView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorRatioToolView.swift; path = Sources/HXPhotoPicker/Editor/View/CropSize/EditorRatioToolView.swift; sourceTree = "<group>"; };
		BE2A51B48878BC9E292BF33501799144 /* PhotoManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoManager.swift; path = Sources/HXPhotoPicker/Core/Util/PhotoManager.swift; sourceTree = "<group>"; };
		BE803FCCDB346C8B5264CF06CA18C471 /* EditorViewController+Action.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Action.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Action.swift"; sourceTree = "<group>"; };
		C13D44321CF536452C258FA91D0591BB /* CameraViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraViewController.swift; path = Sources/HXPhotoPicker/Camera/Controller/CameraViewController.swift; sourceTree = "<group>"; };
		C15AFE57CE5A467ED2A4BF3A817935AA /* EditorMaskView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMaskView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorMaskView.swift"; sourceTree = "<group>"; };
		C1EBE3C61A5B2F6140C339912406657D /* PhotoBrowser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoBrowser.swift; path = Sources/HXPhotoPicker/Picker/Controller/Browser/PhotoBrowser.swift; sourceTree = "<group>"; };
		C2C358EB7A8F7B2162E977322E2FD4E4 /* PhotoPickerController+Internal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerController+Internal.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoPickerController+Internal.swift"; sourceTree = "<group>"; };
		C56BF509892A4AF72B38428504A6C43F /* PhotoDeniedAuthorization.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoDeniedAuthorization.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoDeniedAuthorization.swift; sourceTree = "<group>"; };
		C5B92DF75FC596DE9EB8FC8D2DE44D29 /* EditorBrushSizeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorBrushSizeView.swift; path = Sources/HXPhotoPicker/Editor/View/Brush/EditorBrushSizeView.swift; sourceTree = "<group>"; };
		C5D3F303175A23D5817E0B910B39D591 /* Pods-ObjCSwiftDemo-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-ObjCSwiftDemo-dummy.m"; sourceTree = "<group>"; };
		C6DA0B4C54C3AB43E54B7F029910B3C2 /* EditorChartletPreviewView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorChartletPreviewView.swift; path = Sources/HXPhotoPicker/Editor/View/Chartlet/EditorChartletPreviewView.swift; sourceTree = "<group>"; };
		C7F242A5DB974C77A812451336E03077 /* EditorAdjusterView+Croper.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+Croper.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+Croper.swift"; sourceTree = "<group>"; };
		C826B4BA45AB750643AEF6B4EF83599C /* PhotoAlbumList.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumList.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/AlbumList/PhotoAlbumList.swift; sourceTree = "<group>"; };
		C837D2DE762765C742523A3290DAB53E /* PhotoListCellConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoListCellConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/PhotoListCellConfiguration.swift; sourceTree = "<group>"; };
		******************************** /* SliderView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SliderView.swift; path = Sources/HXPhotoPicker/Core/View/VideoPlayer/SliderView.swift; sourceTree = "<group>"; };
		C93160239E4BE981F01C4495C27DDA36 /* JDTImageModule.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JDTImageModule.release.xcconfig; sourceTree = "<group>"; };
		C9C3C572B7347BC237964D184C0D614E /* AlbumViewBaseCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumViewBaseCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/Album/AlbumViewBaseCell.swift; sourceTree = "<group>"; };
		C9EE28813A5C6684267B19D131168519 /* EditedResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditedResult.swift; path = Sources/HXPhotoPicker/Editor/EditedResult.swift; sourceTree = "<group>"; };
		CA0FA23181E1B0F518C7A51B4343E598 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		CAE2B41F0BC0E7EE8ECB33D79F45B790 /* PreviewVideoControlViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PreviewVideoControlViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PreviewVideoControlViewCell.swift; sourceTree = "<group>"; };
		CB1CA96220C9601CD51E0FB92468894F /* PassThrough.metal */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.metal; name = PassThrough.metal; path = Sources/HXPhotoPicker/Camera/View/PassThrough.metal; sourceTree = "<group>"; };
		CB245637C4B63307AC6BE18D3A0D612B /* PhotoPickerControllerAnimationTransitioning.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerControllerAnimationTransitioning.swift; path = Sources/HXPhotoPicker/Picker/Protocol/Transition/PhotoPickerControllerAnimationTransitioning.swift; sourceTree = "<group>"; };
		CBA12623DE8AE03143F3AA7E76EBCC54 /* PhotoPickerList.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerList.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerList.swift; sourceTree = "<group>"; };
		CC3F61EF1E83C226994151C4DA44EEAA /* HXTableView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HXTableView.swift; path = Sources/HXPhotoPicker/Core/View/HXTableView.swift; sourceTree = "<group>"; };
		CD90FAB3042955AC4E9305B33EE222FD /* PhotoAlbumCollectionCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumCollectionCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/Album/PhotoAlbumCollectionCell.swift; sourceTree = "<group>"; };
		CD92419874653D9402260A514AE16E6B /* EditorMusicViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMusicViewCell.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorMusicViewCell.swift; sourceTree = "<group>"; };
		CE7EDBEA358AB872EABC06AAE5CB0CB1 /* EditorAdjusterView+Edit.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorAdjusterView+Edit.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView+Edit.swift"; sourceTree = "<group>"; };
		CF237DA868C1DC44FBBCB74C835EF355 /* ArrowViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ArrowViewConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/ArrowViewConfiguration.swift; sourceTree = "<group>"; };
		CFA90E32D003CA214BE709B7198AA294 /* AssetManager+Asset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+Asset.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+Asset.swift"; sourceTree = "<group>"; };
		CFD53186AF99D52B405D51C10468FC7C /* ResourceBundle-JDTImageModule-JDTImageModule-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-JDTImageModule-JDTImageModule-Info.plist"; sourceTree = "<group>"; };
		CFE26E3EDB6CDDE1FDBE203769F6C4F9 /* PhotoListConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoListConfiguration.swift; path = Sources/HXPhotoPicker/Picker/Config/PhotoListConfiguration.swift; sourceTree = "<group>"; };
		D1857AC1826A33B35FBA00100533048B /* PhotoPreviewSelectedView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewSelectedView.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoPreviewSelectedView.swift; sourceTree = "<group>"; };
		D1CB2DE05E19C0060AFC9A15A5DC3624 /* EditorMusicLyricViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMusicLyricViewCell.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorMusicLyricViewCell.swift; sourceTree = "<group>"; };
		D23429C610772852F9E743D8AA00E035 /* PhotoPickerView+Cell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+Cell.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+Cell.swift"; sourceTree = "<group>"; };
		D323956F59765864F203FA3F822BF6EE /* JDTImageModule-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JDTImageModule-prefix.pch"; sourceTree = "<group>"; };
		D35F35F5F6F97DECE3DA8DF781DDC37C /* PhotoPickerListConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListConfig.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerListConfig.swift; sourceTree = "<group>"; };
		D3940325C2B5A170D6AC310F478165C5 /* EditorStickersContentView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorStickersContentView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorStickersContentView.swift"; sourceTree = "<group>"; };
		D46473C225BD60D66F3589E97477E36B /* PhotoTools+Camera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoTools+Camera.swift"; path = "Sources/HXPhotoPicker/Core/Util/PhotoTools+Camera.swift"; sourceTree = "<group>"; };
		D54CC9078459F968A300E298851EBD83 /* PhotoPreviewViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPreviewViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Preview/PhotoPreviewViewController.swift; sourceTree = "<group>"; };
		D56C056B4F68F3C6A1361F436E9F0B46 /* PhotoTools+Alert.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoTools+Alert.swift"; path = "Sources/HXPhotoPicker/Core/Util/PhotoTools+Alert.swift"; sourceTree = "<group>"; };
		D57B0ECA4C90696B5784C169B591ADDD /* AssetManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssetManager.swift; path = Sources/HXPhotoPicker/Picker/Util/AssetManager.swift; sourceTree = "<group>"; };
		D616C9955B8A7FD19177B2A4077686F1 /* PhotoAsset+Editor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Editor.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Editor.swift"; sourceTree = "<group>"; };
		D72613C404D0409B2BBA480A1EA31504 /* EditorView+PhotoTools.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+PhotoTools.swift"; path = "Sources/HXPhotoPicker/Editor+View/Util/EditorView+PhotoTools.swift"; sourceTree = "<group>"; };
		D73B3F385CEC6393D0C48C73B152A263 /* Picker+UIViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+UIViewController.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+UIViewController.swift"; sourceTree = "<group>"; };
		D7EF51C12D0FA4B39CF1AAFE766D3731 /* EditorBrushBlockView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorBrushBlockView.swift; path = Sources/HXPhotoPicker/Editor/View/Brush/EditorBrushBlockView.swift; sourceTree = "<group>"; };
		D81301DEDDBA2299229CA5BE396FAF19 /* EditorViewController+Mosaic.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Mosaic.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Mosaic.swift"; sourceTree = "<group>"; };
		D8256C64E6C752D404E0A9C34C5D1BE3 /* PhotoDebugLogsConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoDebugLogsConfig.swift; path = Sources/HXPhotoPicker/Core/Config/PhotoDebugLogsConfig.swift; sourceTree = "<group>"; };
		D8A088AAD9846B0B3B4212A8782C8698 /* PhotoAsset+Video.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Video.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Video.swift"; sourceTree = "<group>"; };
		D8D93421E2B5D1BDD506143F05509416 /* CameraNormalPreviewView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraNormalPreviewView.swift; path = Sources/HXPhotoPicker/Camera/View/CameraNormalPreviewView.swift; sourceTree = "<group>"; };
		D9042614104217D39CBE9333FE1E93E7 /* SwiftPicker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = SwiftPicker.swift; sourceTree = "<group>"; };
		DA3BA9322CB4F79368082AE32FD17625 /* EditorFrameView+Control.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorFrameView+Control.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorFrameView+Control.swift"; sourceTree = "<group>"; };
		DB747034427E6C9D3F8EDF6B0074397A /* PhotoPickerListCondition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerListCondition.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoList/PhotoPickerListCondition.swift; sourceTree = "<group>"; };
		DBDA4EE84FBF358E9F6F772CC945A8C6 /* AlbumListView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumListView.swift; path = Sources/HXPhotoPicker/Picker/View/Album/AlbumListView.swift; sourceTree = "<group>"; };
		DBED3E327BC138821C2C07BE93160C03 /* JDTImageModule.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JDTImageModule.debug.xcconfig; sourceTree = "<group>"; };
		DBF247F9682F6DB63727C57A707D5DB1 /* PickerTransition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerTransition.swift; path = Sources/HXPhotoPicker/Picker/Transition/PickerTransition.swift; sourceTree = "<group>"; };
		DC20FD29AD5E6CDFE0C4AD2EE79DA360 /* AssetManager+PlayerItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+PlayerItem.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+PlayerItem.swift"; sourceTree = "<group>"; };
		DC32F67FE6674420C8095C93D2E7FE88 /* EditorStickerTextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorStickerTextView.swift; path = Sources/HXPhotoPicker/Editor/View/Text/EditorStickerTextView.swift; sourceTree = "<group>"; };
		DD3E5E2509AEC5E6186CDF3D555F01D1 /* PhotoFetchAsset.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoFetchAsset.swift; path = Sources/HXPhotoPicker/Picker/Protocol/Fetch/PhotoFetchAsset.swift; sourceTree = "<group>"; };
		DD928A7EDEC6704F7620C56CDCE82173 /* PhotoAlbumViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoAlbumViewController.swift; path = Sources/HXPhotoPicker/Picker/Controller/Album/PhotoAlbumViewController.swift; sourceTree = "<group>"; };
		DE633388BB93243DEDBBC5AB30A8440F /* TextManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TextManager.swift; path = Sources/HXPhotoPicker/Core/Util/TextManager.swift; sourceTree = "<group>"; };
		DFB1FDE41AB9228BAB074037B242C24A /* Pods_ObjCSwiftDemo.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ObjCSwiftDemo.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E0830D19A6308315D771E8A1ED741D76 /* PhotoPickerViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/PhotoPickerViewCell.swift; sourceTree = "<group>"; };
		E084AF0B5055E5809688BD222BDEEB84 /* HXLog.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HXLog.swift; path = Sources/HXPhotoPicker/Core/Model/HXLog.swift; sourceTree = "<group>"; };
		E11883FCF15058AA2511443DD7B79548 /* AlbumViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumViewCell.swift; path = Sources/HXPhotoPicker/Picker/View/Cell/Album/AlbumViewCell.swift; sourceTree = "<group>"; };
		E1C2D2125819A59968D4F278869202D4 /* EditorView+Public.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorView+Public.swift"; path = "Sources/HXPhotoPicker/Editor+View/View/EditorView+Public.swift"; sourceTree = "<group>"; };
		E47BA054C8BFFBB6A6789DA343121110 /* PhotoPickerFinishItemView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoPickerFinishItemView.swift; path = Sources/HXPhotoPicker/Picker/View/PhotoPickerFinishItemView.swift; sourceTree = "<group>"; };
		E47C190F455C50D813B837ED03A6DB8F /* EditorVolumeView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorVolumeView.swift; path = Sources/HXPhotoPicker/Editor/View/Video/EditorVolumeView.swift; sourceTree = "<group>"; };
		E48588F47A38757CB1F151D71B4230FE /* Core+NSObject.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+NSObject.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+NSObject.swift"; sourceTree = "<group>"; };
		E4C4C22A0FB5ACB79DC6700191E54B09 /* PhotosUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PhotosUI.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/PhotosUI.framework; sourceTree = DEVELOPER_DIR; };
		E6196CCBA9D405C4C3168A30537299D9 /* EditorViewController+Await.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Await.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Await.swift"; sourceTree = "<group>"; };
		E647DC1FA5D26CE60119315DB1DE4A49 /* PhotoControllerEvent.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoControllerEvent.swift; path = Sources/HXPhotoPicker/Picker/Protocol/UI/PhotoControllerEvent.swift; sourceTree = "<group>"; };
		E699FA4A5EE48FF91EBB54DECC895251 /* PhotoPickerViewController+PhotoList.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerViewController+PhotoList.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Picker/PhotoPickerViewController+PhotoList.swift"; sourceTree = "<group>"; };
		E839F48FFB861BAFC90C07D57D3B44C1 /* PickerTypes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PickerTypes.swift; path = Sources/HXPhotoPicker/Picker/Model/PickerTypes.swift; sourceTree = "<group>"; };
		E84FC07A93C5FC648633FD090EFCA2B3 /* EditorPlayAuido.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorPlayAuido.swift; path = Sources/HXPhotoPicker/Editor/Model/EditorPlayAuido.swift; sourceTree = "<group>"; };
		E8B990D88767583534A7DA8B3F06FEA2 /* EditorAdjusterView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorAdjusterView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorAdjusterView.swift"; sourceTree = "<group>"; };
		E8C67DC298675C064968142DBFC591EE /* Editor+CIImage.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Editor+CIImage.swift"; path = "Sources/HXPhotoPicker/Editor/Extension/Editor+CIImage.swift"; sourceTree = "<group>"; };
		E8FAB15BFAE7A24E7577FB03B793FA6D /* PhotoToolBarView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PhotoToolBarView.swift; path = Sources/HXPhotoPicker/Picker/View/ToolBar/PhotoToolBarView.swift; sourceTree = "<group>"; };
		E952CC3A5EC689451CFAE84702F64631 /* PhotoAsset+Local.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoAsset+Local.swift"; path = "Sources/HXPhotoPicker/Picker/Model/PhotoAsset/PhotoAsset+Local.swift"; sourceTree = "<group>"; };
		E9F53129D099F4CD9F7E057C869CA57C /* CameraPreviewView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CameraPreviewView.swift; path = Sources/HXPhotoPicker/Camera/View/CameraPreviewView.swift; sourceTree = "<group>"; };
		EC1A5AAA0EBE9051923DB55040755C65 /* EditorStickerTextView+Draw.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorStickerTextView+Draw.swift"; path = "Sources/HXPhotoPicker/Editor/View/Text/EditorStickerTextView+Draw.swift"; sourceTree = "<group>"; };
		EC257FD53CE9981794E1907003750498 /* Pods-ObjCSwiftDemo-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-ObjCSwiftDemo-umbrella.h"; sourceTree = "<group>"; };
		ED6704C7247DAD1DC24FF6D2E21B1CF6 /* EditorToolsView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorToolsView.swift; path = Sources/HXPhotoPicker/Editor/View/EditorToolsView.swift; sourceTree = "<group>"; };
		EDE2BAED3C9DA439ED017DF8480D79B0 /* EditorViewController+Music.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EditorViewController+Music.swift"; path = "Sources/HXPhotoPicker/Editor/Controller/EditorViewController+Music.swift"; sourceTree = "<group>"; };
		EEFD90C3AA11B20E0C3458043619A03B /* HXCollectionView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HXCollectionView.swift; path = Sources/HXPhotoPicker/Core/View/HXCollectionView.swift; sourceTree = "<group>"; };
		F0287CE4F49D241FB9C0D4B592BE89E0 /* VideoPlayerView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VideoPlayerView.swift; path = Sources/HXPhotoPicker/Core/View/VideoPlayer/VideoPlayerView.swift; sourceTree = "<group>"; };
		F3009C554D917A450D3FBA61DD02AC8C /* EditorViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorViewController.swift; path = Sources/HXPhotoPicker/Editor/Controller/EditorViewController.swift; sourceTree = "<group>"; };
		F30F7A970ADB2A91AD48FC7585596FDE /* AlbumTitleView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AlbumTitleView.swift; path = Sources/HXPhotoPicker/Picker/View/Album/AlbumTitleView.swift; sourceTree = "<group>"; };
		F4C3FB76FE244C0AC4B23E5AE81BEFA1 /* PhotoPickerController+PickerData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerController+PickerData.swift"; path = "Sources/HXPhotoPicker/Picker/Controller/Photo/PhotoPickerController+PickerData.swift"; sourceTree = "<group>"; };
		F5A28B03430782E79CF5B5A9821A53E5 /* Core+UIColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+UIColor.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+UIColor.swift"; sourceTree = "<group>"; };
		F63525793BC65420295CDA52FFC23D86 /* Core+CALayer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+CALayer.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+CALayer.swift"; sourceTree = "<group>"; };
		F6DC3B5FBAA2F684B64BB7B512B57442 /* Pods-ObjCSwiftDemo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ObjCSwiftDemo.release.xcconfig"; sourceTree = "<group>"; };
		F7E2222F451C30E1968041519BD47E69 /* HXPhotoPicker.bundle */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = "wrapper.plug-in"; name = HXPhotoPicker.bundle; path = Sources/HXPhotoPicker/Resources/HXPhotoPicker.bundle; sourceTree = "<group>"; };
		F88E3547CB6482B8A1478B48B67DC409 /* EditorMaskListViewCell.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorMaskListViewCell.swift; path = Sources/HXPhotoPicker/Editor/View/CropSize/EditorMaskListViewCell.swift; sourceTree = "<group>"; };
		F91B5C170D89D912AD9E1CD05F85F382 /* EditorStickersView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EditorStickersView.swift; path = "Sources/HXPhotoPicker/Editor+View/View/EditorStickersView.swift"; sourceTree = "<group>"; };
		FD47D725FDFC90C237F87E7786745ABA /* AssetManager+LivePhoto.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AssetManager+LivePhoto.swift"; path = "Sources/HXPhotoPicker/Picker/Util/AssetManager+LivePhoto.swift"; sourceTree = "<group>"; };
		FD9390C93CE525ED5F7F409A50C6020A /* PhotoPickerView+Camera.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PhotoPickerView+Camera.swift"; path = "Sources/HXPhotoPicker/Picker/PickerView/PhotoPickerView+Camera.swift"; sourceTree = "<group>"; };
		FD9ACD2CC4741B6C2EC865B1B37E93FD /* Picker+Array.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Picker+Array.swift"; path = "Sources/HXPhotoPicker/Picker/Extension/Picker+Array.swift"; sourceTree = "<group>"; };
		FDA004B1012EAEBDCE22DA7ED0AC71B7 /* AssetResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssetResult.swift; path = Sources/HXPhotoPicker/Picker/AssetResult.swift; sourceTree = "<group>"; };
		FE2B5106FE3DAED048A89AC0B4DAEB7C /* Core+String.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Core+String.swift"; path = "Sources/HXPhotoPicker/Core/Extension/Core+String.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		499162BE6A5970090821A5C213740A29 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29BC07E59105A0DBA36F3771767C60C8 /* Foundation.framework in Frameworks */,
				8991B758B946E9B82B04C939F2E09281 /* Photos.framework in Frameworks */,
				A47F066D8AF04455D29C0EFD193DFCA0 /* PhotosUI.framework in Frameworks */,
				E017E7FAA34D6C1AB8FBDE40368E07BC /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4BF81BA82339E5E3D243986FF48C1CD9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		534CA04219186E31829FF33A34555292 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				975470A1A55498FCF2C3F22CEAA402F4 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		88F42E01B259355402E964BB8406B7AC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C2165BBC3D03B20079526E3697299B0D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1DEF607217D3613AF9AB209DC23A6D8D /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		15439A1BAE8C3502912AC53A143FA128 /* Core */ = {
			isa = PBXGroup;
			children = (
				807C4D50EA3E351ECA6D86909712769C /* AppearanceStyle.swift */,
				2F6CA6B226ECED60A2DBDCA59945C970 /* AssetPermissionsUtil.swift */,
				3E681373CE7E092A140F20821A4BE159 /* AssetSaveUtil.swift */,
				19A0A8F9EB0E61AE1A79E72DC25B6615 /* Core+AVAsset.swift */,
				38B19E205EADA4E98976410EDF249E9A /* Core+Bundle.swift */,
				F63525793BC65420295CDA52FFC23D86 /* Core+CALayer.swift */,
				B98857619009286532DF174050ED3973 /* Core+CGFloat.swift */,
				AC939ECBBB358DFEB87C5A343C18A949 /* Core+Data.swift */,
				221550860117559284BE64A15F040FDF /* Core+Dictionary.swift */,
				7984854A9E6B4963A93F19EBC7B68D5C /* Core+FileManager.swift */,
				3E17A40D70CC2BE7E99CEC723827E338 /* Core+LayerRTLFrame.swift */,
				E48588F47A38757CB1F151D71B4230FE /* Core+NSObject.swift */,
				FE2B5106FE3DAED048A89AC0B4DAEB7C /* Core+String.swift */,
				21E63CA427D8D83D8A8CBED8F28B0BDB /* Core+UIApplication.swift */,
				684378CFCE2AD25B8D014F502640003B /* Core+UIBarButtonItem.swift */,
				90E70CD912661F1824EF4ABC6B1A2682 /* Core+UICollectionView.swift */,
				F5A28B03430782E79CF5B5A9821A53E5 /* Core+UIColor.swift */,
				1F30093DC54240D9DDE7A029AD8218FE /* Core+UIDevice.swift */,
				354F52A0F0044AE20E410A7D676F143A /* Core+UIFont.swift */,
				63A0C8A37013E36CAE04B87E2D50A6F2 /* Core+UIImage.swift */,
				8222B901A5D698452D79E5B5956FFC34 /* Core+UIImageView.swift */,
				9858C8CB63795012D11DB2823D26F624 /* Core+UILabel.swift */,
				2BCC0C5B079FF7983B6ACE826D0AB1BE /* Core+UITableView.swift */,
				1D7A1AF7E3D9B42D128F2F201E985E4D /* Core+UIView.swift */,
				5EDDD0D27D73280213454C9A65E4F643 /* Core+UIViewController.swift */,
				7A3D1C145E71E4F182E42119EAD815AE /* Core+UIViewRTLFrame.swift */,
				5A9B2FC917F92187A663B16A27348A57 /* Core+URL.swift */,
				9D0DB1AA5EFAAE3CDE271745B07B02EB /* CustomLanguage.swift */,
				8B0E9EEF82C6FE9A3E173E6C2879D0AA /* ExportPreset.swift */,
				23EFA3CC82F9FC4724D85FFE68B6B000 /* HXBaseViewController.swift */,
				EEFD90C3AA11B20E0C3458043619A03B /* HXCollectionView.swift */,
				978829349A370A8F5C784CB41ACDC16D /* HXImageViewProtocol.swift */,
				E084AF0B5055E5809688BD222BDEEB84 /* HXLog.swift */,
				6ED0C3B1CB142719C5CA1CB3FE4762AE /* HXPhotoPicker.swift */,
				CC3F61EF1E83C226994151C4DA44EEAA /* HXTableView.swift */,
				A9E7BD62C42F903F2359F1738E782E76 /* ImageContentType.swift */,
				AE10BE8BC584F1EA0A3AC092B6CED14C /* ImageResource.swift */,
				AA86496717AD4C66D9FADDC12E0E8763 /* ImageViewConfig.swift */,
				33129BB49305C38EAF061BBFF4398D46 /* IndicatorType.swift */,
				ADA0AE8C603E25DB2343D5C9900A4D1B /* LanguageType.swift */,
				D8256C64E6C752D404E0A9C34C5D1BE3 /* PhotoDebugLogsConfig.swift */,
				2368A59124CA95605FB5D47B60CDD613 /* PhotoHUDConfig.swift */,
				8D5B847B39B717ADFF6273BE06162347 /* PhotoHUDProtocol.swift */,
				BE2A51B48878BC9E292BF33501799144 /* PhotoManager.swift */,
				00F354CA817B91147A2B6664B3B3C165 /* PhotoManager+Download.swift */,
				2C443D9D41A72D31A6C6C3FFE92A6C57 /* PhotoManager+Language.swift */,
				53CB4DA0AEAC9C4E53EB1A56095D9C60 /* PhotoPanGestureRecognizer.swift */,
				20DE3E2E1350F88459A354CCF605AFA1 /* PhotoTools.swift */,
				D56C056B4F68F3C6A1361F436E9F0B46 /* PhotoTools+Alert.swift */,
				D46473C225BD60D66F3589E97477E36B /* PhotoTools+Camera.swift */,
				ACB89A1E18FFC9DA950A69DB608CE435 /* PhotoTools+File.swift */,
				AC2BB04A00E5980D08522443BE270631 /* PlayButton.swift */,
				4350B462D8302145E8EA459D1693033D /* ProgressCircleView.swift */,
				AFF094815CCE529B5DCE3302D31E87E0 /* ProgressCricleJoinView.swift */,
				891154F04051668B1F84BE657A8DC04B /* ProgressHUD.swift */,
				5422CA8608B2FA688A7D7D644AB745E6 /* ProgressImageView.swift */,
				374D8409ED6472B64DBB97DD92BF1BE1 /* ProgressIndefiniteView.swift */,
				0D2A755EE3FB70F378E2A108DBDE0388 /* SelectBoxConfiguration.swift */,
				******************************** /* SelectBoxView.swift */,
				******************************** /* SliderView.swift */,
				DE633388BB93243DEDBBC5AB30A8440F /* TextManager.swift */,
				F0287CE4F49D241FB9C0D4B592BE89E0 /* VideoPlayerView.swift */,
				44A2076CF8BCEE5613EC7FE0CDF56CCB /* VideoPlaySliderView.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		1F76793D8EE5482F08295AFC546CA668 /* Picker */ = {
			isa = PBXGroup;
			children = (
				BD8619831D4C378D268F23BD9140F6ED /* AlbumListConfiguration.swift */,
				DBDA4EE84FBF358E9F6F772CC945A8C6 /* AlbumListView.swift */,
				4A169FA30F6F6FCCB8017CE00D7336EF /* AlbumSectionHeaderView.swift */,
				F30F7A970ADB2A91AD48FC7585596FDE /* AlbumTitleView.swift */,
				3720BD45FD06C5CA7DC5A3A23265349B /* AlbumTitleViewConfiguration.swift */,
				C9C3C572B7347BC237964D184C0D614E /* AlbumViewBaseCell.swift */,
				E11883FCF15058AA2511443DD7B79548 /* AlbumViewCell.swift */,
				0379962A2D6FAADB683611C2271421AB /* AlbumViewController.swift */,
				2D80F2BCBF539D3EA75EE54F08418537 /* ArrowView.swift */,
				CF237DA868C1DC44FBBCB74C835EF355 /* ArrowViewConfiguration.swift */,
				60F38BD222663EDB7D58414086C7FF80 /* AssetError.swift */,
				D57B0ECA4C90696B5784C169B591ADDD /* AssetManager.swift */,
				CFA90E32D003CA214BE709B7198AA294 /* AssetManager+Asset.swift */,
				418E12F5E563D168DADA19F334A7DF89 /* AssetManager+AVAsset.swift */,
				A54D7406174AC4D0CE3EFDE0B53751EF /* AssetManager+AVAssetExportSession.swift */,
				3668AB6CABB5FEC0ABCF6210CDD0E3C7 /* AssetManager+Image.swift */,
				B51CCD9558282BD278E2FE04C5E5D55C /* AssetManager+ImageData.swift */,
				31416751C90BDE3CC21B1A4CDDD74C44 /* AssetManager+ImageURL.swift */,
				FD47D725FDFC90C237F87E7786745ABA /* AssetManager+LivePhoto.swift */,
				22E9F2CFA47CE7855970CBDEB669FA97 /* AssetManager+LivePhotoURL.swift */,
				DC20FD29AD5E6CDFE0C4AD2EE79DA360 /* AssetManager+PlayerItem.swift */,
				7FA386A2CAFDE68CBA7593DD43D27286 /* AssetManager+VideoURL.swift */,
				FDA004B1012EAEBDCE22DA7ED0AC71B7 /* AssetResult.swift */,
				9F5F68C9F086246EE2B80A1780765C75 /* AssetURLResult.swift */,
				69047A871878B25CFA264C7DB91FF159 /* CaptureVideoPreviewView.swift */,
				5B67425251B51353AE8A7D4A38019ED0 /* DeniedAuthorizationView.swift */,
				5A81FBE6335FD8A4A976CCD9FD672BC8 /* EmptyView.swift */,
				0978A23FA7F89FCF52E275CA641B3941 /* EmptyViewConfiguration.swift */,
				A4DAECBC434D0EEB28E1B2D18954AD67 /* LivePhotoError.swift */,
				4D064ADDE951DF2CCACF53E53E87982C /* LocalAsset.swift */,
				574A182DB58FE7697656B8FAB2DB50BB /* NetworkAsset.swift */,
				9FAA07E956B3AD5059A8B4053922E36A /* NotAuthorizedConfiguration.swift */,
				CD90FAB3042955AC4E9305B33EE222FD /* PhotoAlbumCollectionCell.swift */,
				91756DAFF4884D2D3E0C9BAA43248DAF /* PhotoAlbumCollectionViewCell.swift */,
				4EA7303E022EA3333F6C26C391E8B29F /* PhotoAlbumController.swift */,
				2DF4FBDBAEE565C17A3D83E5B33E95D3 /* PhotoAlbumControllerConfiguration.swift */,
				300430C0A4A9689485F7ECF4239E7082 /* PhotoAlbumHeaderView.swift */,
				C826B4BA45AB750643AEF6B4EF83599C /* PhotoAlbumList.swift */,
				8FC093A59283E28FE8F6F342859945C9 /* PhotoAlbumViewCell.swift */,
				DD928A7EDEC6704F7620C56CDCE82173 /* PhotoAlbumViewController.swift */,
				0CABA0AC63577DA024753A7B135D2FBF /* PhotoAsset.swift */,
				2A27B00D1A8D0609645BA96BDECDF6D0 /* PhotoAsset+Codable.swift */,
				D616C9955B8A7FD19177B2A4077686F1 /* PhotoAsset+Editor.swift */,
				B41DF9BB358A82B74EE3BE34D88E27FE /* PhotoAsset+Equatable.swift */,
				BB4D8A08D155332EA4B9A3B8721804E8 /* PhotoAsset+FileSize.swift */,
				8FB3244CF8960971D9602C2587E1BDC2 /* PhotoAsset+Image.swift */,
				E952CC3A5EC689451CFAE84702F64631 /* PhotoAsset+Local.swift */,
				B6AF672E111C559E9FA004AB385D877C /* PhotoAsset+Network.swift */,
				B9CAC87B32E89186965BB55B63A5E28E /* PhotoAsset+Request.swift */,
				8E71B2434C727860F1D0634C3C22CD1C /* PhotoAsset+URL.swift */,
				D8A088AAD9846B0B3B4212A8782C8698 /* PhotoAsset+Video.swift */,
				37F795FE155BCD35FD53D08B7A4D0D53 /* PhotoAssetCollection.swift */,
				0FEA8DDE0F6E15644D3A2F19357F7E23 /* PhotoBaseViewController.swift */,
				C1EBE3C61A5B2F6140C339912406657D /* PhotoBrowser.swift */,
				ABF3D7D7A51DF564BC0A442577BF519F /* PhotoBrowserAnimationTransitioning.swift */,
				95F12217D5E6CDEE2AECA748ABA136E6 /* PhotoBrowserAnimator.swift */,
				A3143AC72F045E962D820FECD4E6A918 /* PhotoBrowserInteractiveAnimator.swift */,
				4B1FCD59C785A158552AE6C816877FAC /* PhotoBrowserInteractiveTransition.swift */,
				E647DC1FA5D26CE60119315DB1DE4A49 /* PhotoControllerEvent.swift */,
				C56BF509892A4AF72B38428504A6C43F /* PhotoDeniedAuthorization.swift */,
				1574B5FA64FB493EA4D959E5BEC4C168 /* PhotoError.swift */,
				DD3E5E2509AEC5E6186CDF3D555F01D1 /* PhotoFetchAsset.swift */,
				561B5B856F55D2AB6B4B1CD77E1E19BE /* PhotoFetchAssetCollection.swift */,
				239166C0B73C4BB5A8DAAC41E96F066E /* PhotoFetchData.swift */,
				4FB35256EFE59BCC86317CC292956190 /* PhotoImageCancelItemView.swift */,
				C837D2DE762765C742523A3290DAB53E /* PhotoListCellConfiguration.swift */,
				CFE26E3EDB6CDDE1FDBE203769F6C4F9 /* PhotoListConfiguration.swift */,
				3C432D845FDB8DD800DC0301FF20E0FE /* PhotoLoadingView.swift */,
				6F0A5625720E3A09F21AD09CD9B6D696 /* PhotoMyAlbumViewController.swift */,
				31DE782466BE5DE2396CBB4DA16CE0D1 /* PhotoNavigationItem.swift */,
				9BE530E5EFC06D9B34EE179F2FE0DA36 /* PhotoPeekViewController.swift */,
				3BBE42120AE6117418279AF9153ED501 /* PhotoPermissionPromptView.swift */,
				2DA9D39D88FA1D552DAE0658AD436F71 /* PhotoPickerBaseViewCell.swift */,
				70530C3EEB787DC2042A8C2C771C312D /* PhotoPickerBottomNumberView.swift */,
				3383F8E2C5FB0994A99CB3CE18A7FFB6 /* PhotoPickerController.swift */,
				C2C358EB7A8F7B2162E977322E2FD4E4 /* PhotoPickerController+Internal.swift */,
				570A91174D6C4846D15DC63C0184C1C5 /* PhotoPickerController+PHPhotoLibrary.swift */,
				F4C3FB76FE244C0AC4B23E5AE81BEFA1 /* PhotoPickerController+PickerData.swift */,
				ACBF2803ADC011D484A1A508DB226C45 /* PhotoPickerController+Transitioning.swift */,
				CB245637C4B63307AC6BE18D3A0D612B /* PhotoPickerControllerAnimationTransitioning.swift */,
				2513C6E38C06FB3EA2DA85C9C481B767 /* PhotoPickerControllerAnimator.swift */,
				99012C6177330A6C13868B40DFF3769C /* PhotoPickerControllerFectch.swift */,
				58A6C3C54FBCE42AC4591797FDBAE095 /* PhotoPickerControllerInteractiveAnimator.swift */,
				3C7C4B5CE5C2BD1DB973232A2C4F251C /* PhotoPickerControllerInteractiveTransition.swift */,
				094DC497053AE73D659B6748B85D89CF /* PhotoPickerControllerProtocol.swift */,
				9BD9A8A607EF9971FC634DB1B34D6D89 /* PhotoPickerData.swift */,
				544413451FE214084FFE9FEC3F1ECE34 /* PhotoPickerDataStatus.swift */,
				2D25073787F2C34465FBD89072A0C8AF /* PhotoPickerFilterItemView.swift */,
				AD35F273D9746B18C4BB6E036FBF455C /* PhotoPickerFilterViewController.swift */,
				E47BA054C8BFFBB6A6789DA343121110 /* PhotoPickerFinishItemView.swift */,
				56E3CB033EDC9733A7E9B4066DFCBA40 /* PhotoPickerLimitCell.swift */,
				CBA12623DE8AE03143F3AA7E76EBCC54 /* PhotoPickerList.swift */,
				8D002CB238D49910630F3ADAFD15AFB0 /* PhotoPickerListAssets.swift */,
				5B278228584CE2103FF6C1F876E4B3E1 /* PhotoPickerListCollectionView.swift */,
				DB747034427E6C9D3F8EDF6B0074397A /* PhotoPickerListCondition.swift */,
				D35F35F5F6F97DECE3DA8DF781DDC37C /* PhotoPickerListConfig.swift */,
				B028000BCCDD071B781E2250027AE9A8 /* PhotoPickerListFectchCell.swift */,
				1667CEE1CB2D196267CD02269FDE9AA3 /* PhotoPickerListSwipeSelect.swift */,
				5BF8B40CF7294D1A6B830858A212D16F /* PhotoPickerListViewController.swift */,
				74E7EB7A97AE8D6A4851E9E76EE5DC8F /* PhotoPickerNavigationTitle.swift */,
				B6121E4C9EE4DAB7308F516C45E8E22E /* PhotoPickerPageViewController.swift */,
				A7666775D8F08F87AE8FB5215AC54E79 /* PhotoPickerSelectableViewCell.swift */,
				7DEFE13B97AFC1F24F4FC06783E7C986 /* PhotoPickerSwitchLayout.swift */,
				BD7E8C4AACC66454A525FB1243A9325B /* PhotoPickerView.swift */,
				6C0DF4B5D571D1B934FA77506277EE0A /* PhotoPickerView+Asset.swift */,
				FD9390C93CE525ED5F7F409A50C6020A /* PhotoPickerView+Camera.swift */,
				D23429C610772852F9E743D8AA00E035 /* PhotoPickerView+Cell.swift */,
				7A7EC1CE5A1231D6A9C716A27FB4B7CD /* PhotoPickerView+CollectionView.swift */,
				3EC4A7EB7CE9B8A383291613A329FCC8 /* PhotoPickerView+Editor.swift */,
				7F251867819303E98BCB2ACAC5669D20 /* PhotoPickerView+Function.swift */,
				3B49524577C66C9983E155A59E3F32D2 /* PhotoPickerView+Preview.swift */,
				E0830D19A6308315D771E8A1ED741D76 /* PhotoPickerViewCell.swift */,
				3EE3D52CE843517C540AD7A187873AC2 /* PhotoPickerViewController.swift */,
				1FFE3F1461D4D7A2F119186A8080AEE5 /* PhotoPickerViewController+AlbumView.swift */,
				4D06151F50611D629C971B8BC33B3808 /* PhotoPickerViewController+Camera.swift */,
				9DA28EC100612F848DA85881AC52C0C0 /* PhotoPickerViewController+Editor.swift */,
				8C8506A89DE0C7792CCF6A4B4934690A /* PhotoPickerViewController+FetchAsset.swift */,
				E699FA4A5EE48FF91EBB54DECC895251 /* PhotoPickerViewController+PhotoList.swift */,
				0FAAF2E75DF1E3AD9A49941BE467336B /* PhotoPickerViewController+Preview.swift */,
				87426884B220B437C2317FA73CF0B5E9 /* PhotoPickerViewController+Toolbar.swift */,
				5C4F0A230FC76EEAAB91AA8CFAFEE25A /* PhotoPickerViewProtocol.swift */,
				93F381FD5C99DE9D1F78DED92F32E1A8 /* PhotoPickerWeChatViewCell.swift */,
				B6F505333A0F849FEEECA136226F47AE /* PhotoPreviewContentLivePhotoView.swift */,
				52B7E9208D24DE8C9A2C2749922B9D78 /* PhotoPreviewContentPhotoView.swift */,
				07ADB42218344B49768EAD997C143320 /* PhotoPreviewContentVideoView.swift */,
				7BFD29E391E22EE680A7699DA35A0B43 /* PhotoPreviewContentViewProtocol.swift */,
				2BCCA0B9085E08D1CBBA2ADDA1DEA6B1 /* PhotoPreviewListView.swift */,
				B269ACD9FC933988CDB3751B959D0D61 /* PhotoPreviewListViewCell.swift */,
				388B0C7C04F8004672033964FF13F190 /* PhotoPreviewListViewLayout.swift */,
				D1857AC1826A33B35FBA00100533048B /* PhotoPreviewSelectedView.swift */,
				A3B616E9E3990DEF8AD9C998E20ACD70 /* PhotoPreviewSelectedViewCell.swift */,
				8517B133E25CDB1B7DF00FA567D5D57A /* PhotoPreviewVideoView.swift */,
				285F38BF41ACC86F4631E429089F69E8 /* PhotoPreviewViewCell.swift */,
				D54CC9078459F968A300E298851EBD83 /* PhotoPreviewViewController.swift */,
				64CAB42769526B703114F62BA54294B0 /* PhotoPreviewViewController+CollectionView.swift */,
				7D1A2DCD5A6F0435D9169278412A2AC0 /* PhotoPreviewViewController+Editor.swift */,
				991EB681C7EC3345C2100833B424F65C /* PhotoPreviewViewController+NavigationController.swift */,
				7771F644EC541C4B1928477071709DC9 /* PhotoPreviewViewController+SelectBox.swift */,
				******************************** /* PhotoPreviewViewController+Toolbar.swift */,
				******************************** /* PhotoPreviewViewControllerProtocol.swift */,
				8D0285F28163890D20C1C5FAA3E5E506 /* PhotoSplitViewController.swift */,
				05383888EAD2C595B7B64CED16E364BD /* PhotoTextCancelItemView.swift */,
				887E8C30595E12D37E1C57819C00A1EC /* PhotoThumbnailView.swift */,
				243CAB4B07D2DF72404C1C9E31DB06C1 /* PhotoToolBar.swift */,
				03841F568EDB504253E5A557B4B435BD /* PhotoToolBarEmptyView.swift */,
				E8FAB15BFAE7A24E7577FB03B793FA6D /* PhotoToolBarView.swift */,
				FD9ACD2CC4741B6C2EC865B1B37E93FD /* Picker+Array.swift */,
				265C677F21C6633D856E4F28AD8A1EFD /* Picker+ConfigExtension.swift */,
				745BDB2F10187F02CE1A4F6111908D43 /* Picker+Int.swift */,
				56048137FCB6FC7A1596045363BCD503 /* Picker+LivePhotoTools.swift */,
				80E9543D306FF7D79E78EAF673F58FF7 /* Picker+PHAsset.swift */,
				8CA16080C639896A2CD61AC4F98F8343 /* Picker+PHAssetCollection.swift */,
				709B4104624C830A8111F35B2E3635CA /* Picker+PhotoAsset.swift */,
				4FD1C4E06593919519B1D9F580D76EF4 /* Picker+PhotoManager.swift */,
				59192AD9A197F2CBF49D979E61BBCBCF /* Picker+PhotoTools.swift */,
				710B03E0738E38F4038E5859A93B18B4 /* Picker+UIImageView.swift */,
				D73B3F385CEC6393D0C48C73B152A263 /* Picker+UIViewController.swift */,
				8541F4B9A66C5874778B820DD6761138 /* PickerBottomViewConfiguration.swift */,
				BC71C725F1D8D2CC11EE2C719F7D0439 /* PickerCameraViewCell.swift */,
				37B072392549BF2CAF4368B476DF02D5 /* PickerConfiguration.swift */,
				27827C41841C74D4E098A0DF83D451CF /* PickerInteractiveTransition.swift */,
				2E28BEF6DA14C2C9EF3B674F8182DFBE /* PickerManager.swift */,
				B95858DD73C99FD8E75B920B2FA910E2 /* PickerResult.swift */,
				DBF247F9682F6DB63727C57A707D5DB1 /* PickerTransition.swift */,
				E839F48FFB861BAFC90C07D57D3B44C1 /* PickerTypes.swift */,
				B6696A56ACAAD989023AEBB20B528DD8 /* PreviewLivePhotoViewCell.swift */,
				2F93BD37C3D8479C651D62C436F4981F /* PreviewPhotoViewCell.swift */,
				CAE2B41F0BC0E7EE8ECB33D79F45B790 /* PreviewVideoControlViewCell.swift */,
				656BC291DE4861E74F84D666CB0F469B /* PreviewVideoViewCell.swift */,
				02F7F69D8D5303057827F2CCCDFC14B4 /* PreviewViewConfiguration.swift */,
				3D93E8511537AE296CC1F4787ED520D1 /* SystemCameraConfiguration.swift */,
				BDE9435DDA3E22DE1F324BC6E9FB97D7 /* SystemCameraViewController.swift */,
				ADA96359B286FFEEDE25CDA2AAD9020A /* TickView.swift */,
			);
			name = Picker;
			sourceTree = "<group>";
		};
		3328FE722D4A1C7022B46881AFBE60F0 /* HXPhotoPicker */ = {
			isa = PBXGroup;
			children = (
				81B217B6F9874C4C7B1A2D66ED7B4B2D /* Camera */,
				15439A1BAE8C3502912AC53A143FA128 /* Core */,
				C7EBB70BE09FE939BDEDFC6C1B055B20 /* Editor */,
				969CF788249AE7B4D14FCC10308D1054 /* EditorView */,
				1F76793D8EE5482F08295AFC546CA668 /* Picker */,
				3948240892686E7FDE5BC533C3D4D7B7 /* Resources */,
				D25ECB46F256B1F561BE4EAEA60DDB36 /* Support Files */,
			);
			path = HXPhotoPicker;
			sourceTree = "<group>";
		};
		3948240892686E7FDE5BC533C3D4D7B7 /* Resources */ = {
			isa = PBXGroup;
			children = (
				C575145CD3DFBA05E4D87F845BC54A87 /* Resources */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		4E019FB1F70649B146CEF7829FE54924 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				4F0AB9285ABD82D2858A40799C799664 /* Pods-ObjCSwiftDemo */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		4F0AB9285ABD82D2858A40799C799664 /* Pods-ObjCSwiftDemo */ = {
			isa = PBXGroup;
			children = (
				76E4DCB71CDB143EAB86CFB104768646 /* Pods-ObjCSwiftDemo.modulemap */,
				664F4237C8BE4A9420895261099D34DA /* Pods-ObjCSwiftDemo-acknowledgements.markdown */,
				B51817FD2FEB3E43633075C3EF92BF88 /* Pods-ObjCSwiftDemo-acknowledgements.plist */,
				C5D3F303175A23D5817E0B910B39D591 /* Pods-ObjCSwiftDemo-dummy.m */,
				8C5519BCAE6833CFDC4A2F949BE4A61F /* Pods-ObjCSwiftDemo-frameworks.sh */,
				4825913F5CD8AB967F9B695226CDFAE4 /* Pods-ObjCSwiftDemo-Info.plist */,
				EC257FD53CE9981794E1907003750498 /* Pods-ObjCSwiftDemo-umbrella.h */,
				6151F9B26EA26FD1DAF8AE03B74B9D97 /* Pods-ObjCSwiftDemo.debug.xcconfig */,
				F6DC3B5FBAA2F684B64BB7B512B57442 /* Pods-ObjCSwiftDemo.release.xcconfig */,
			);
			name = "Pods-ObjCSwiftDemo";
			path = "Target Support Files/Pods-ObjCSwiftDemo";
			sourceTree = "<group>";
		};
		5C4DFFE1E7202271E65CD04B8216A5C3 /* Pod */ = {
			isa = PBXGroup;
			children = (
				A1E58FD5F48743D565FE2B3158DAD4B9 /* JDTImageModule.podspec */,
				6BE91D8479997127D6409DC89B96933D /* LICENSE */,
				A3620F1826196D3CF6A3C5E46799F5A5 /* README.md */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		6E4EA2D3C98457F96E55D5E280079E48 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				9317E0767D58C6948FC7C0BAC47310D2 /* JDTImageModule */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		6ED1B78986B855CE00C54F71D7D5595E /* Products */ = {
			isa = PBXGroup;
			children = (
				24DDD4A0849B06C0942057220E1E81B2 /* HXPhotoPicker.framework */,
				07FCAF745F3EEC27684AB03948F3A3EE /* HXPhotoPicker_Privacy.bundle */,
				3266178C3986BEA7E3DE52558B843164 /* JDTImageModule.framework */,
				881094C4979E410E6AF6E86318DA4F98 /* JDTImageModule.bundle */,
				DFB1FDE41AB9228BAB074037B242C24A /* Pods_ObjCSwiftDemo.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		81B217B6F9874C4C7B1A2D66ED7B4B2D /* Camera */ = {
			isa = PBXGroup;
			children = (
				D2C6E045ABF7EFED045FF97102C32BD8 /* Lite */,
				DF07D376D1BCEE438B530F519AAFDC1A /* Location */,
			);
			name = Camera;
			sourceTree = "<group>";
		};
		9317E0767D58C6948FC7C0BAC47310D2 /* JDTImageModule */ = {
			isa = PBXGroup;
			children = (
				2C5D77296D1951C4C87F7F80C91549A1 /* JDTImageModule.h */,
				3474C413E03BAB49A675FA270CA6943F /* JDTImageModule.m */,
				B4D06939873FD0776578A2D42BCA9062 /* Picker */,
				5C4DFFE1E7202271E65CD04B8216A5C3 /* Pod */,
				F4CC24A86AC40B39DD680BAD058AE48B /* Support Files */,
			);
			name = JDTImageModule;
			path = ../JDTImageModule;
			sourceTree = "<group>";
		};
		969CF788249AE7B4D14FCC10308D1054 /* EditorView */ = {
			isa = PBXGroup;
			children = (
				E8B990D88767583534A7DA8B3F06FEA2 /* EditorAdjusterView.swift */,
				931D28714877B0595FAFF2988CAD21D1 /* EditorAdjusterView+ContentView.swift */,
				C7F242A5DB974C77A812451336E03077 /* EditorAdjusterView+Croper.swift */,
				CE7EDBEA358AB872EABC06AAE5CB0CB1 /* EditorAdjusterView+Edit.swift */,
				22CD92FB8531CBB61F425912330F0C19 /* EditorAdjusterView+FrameView.swift */,
				A3F574521FD9FE506349F7BD4C56E601 /* EditorAdjusterView+Mirror.swift */,
				72208964807741AD183F3E36D83A90A7 /* EditorAdjusterView+Rotate.swift */,
				BD5968928B9F0760538F06AD6D8AC691 /* EditorAdjusterView+ScreenRotation.swift */,
				8AF58A82392843352FC90EF232DD7568 /* EditorAdjusterView+ScrollView.swift */,
				7D4CE2129947066D5EE5C99EB05F726C /* EditorAdjusterView+Video.swift */,
				AE8B055FA8006491BCABC86DC2D0EA75 /* EditorAdjusterViewProtocol.swift */,
				9C87B621086B1C54011333599EFF2723 /* EditorAudioAnimationView.swift */,
				00C43FF90F67084C11C94811FA62D7E9 /* EditorCanvasView.swift */,
				0ED4F6C9D4069955665F6EBF056A37A9 /* EditorContentView.swift */,
				90E82FF5E117D9E926606C741F0D77B3 /* EditorControlView.swift */,
				1B712293FCB50420C681131E67997887 /* EditorDrawTool.swift */,
				7B8095CE0780D4129706728A8452A778 /* EditorDrawView.swift */,
				681EDFAA6E48BA557A3C85A1C4F0228F /* EditorFrameView.swift */,
				DA3BA9322CB4F79368082AE32FD17625 /* EditorFrameView+Control.swift */,
				A61E287ACF3EC037C9C36340833BCA4D /* EditorFrameView+VideoPlay.swift */,
				C15AFE57CE5A467ED2A4BF3A817935AA /* EditorMaskView.swift */,
				40AB1A4DDA719AB7A53DE2AB85638BC2 /* EditorModels.swift */,
				86EA281DBA8E46CACFBC2D77FBEEDA60 /* EditorMosaicView.swift */,
				D3940325C2B5A170D6AC310F478165C5 /* EditorStickersContentView.swift */,
				7ADA493E783217B491C3B3F97D4A417C /* EditorStickersItemView.swift */,
				8D400C8DE7A3AA4A130C23BD14B828E9 /* EditorStickersTrashView.swift */,
				F91B5C170D89D912AD9E1CD05F85F382 /* EditorStickersView.swift */,
				A381EBE707A2E2E5D34AF239D383538D /* EditorTypes.swift */,
				165848B829E7D377966C7F5A8418690C /* EditorVideoCompositor.swift */,
				1D59F5CD82CC8D5E6AEDDB7B9FA1989E /* EditorVideoPlayerView.swift */,
				68D33C4364FB684AFD506497AA7BB264 /* EditorVideoTool.swift */,
				6A35E34E546734B7D01B703E5B888E06 /* EditorView.swift */,
				AB5D5527F2F44719CC3E71843A22A15B /* EditorView+AdjusterView.swift */,
				02E3FCCEFC4531542946F1FD06749773 /* EditorView+AVAsset.swift */,
				AB77DD56A5062F7655E8C4E992737F63 /* EditorView+CGFloat.swift */,
				80D6AA4452E70310081F4712F4BFA513 /* EditorView+CIImage.swift */,
				25B65F9DDDCDD90E43BE7797FED3D5C0 /* EditorView+GestureRecognizer.swift */,
				D72613C404D0409B2BBA480A1EA31504 /* EditorView+PhotoTools.swift */,
				E1C2D2125819A59968D4F278869202D4 /* EditorView+Public.swift */,
				65A82F18C2977BBCF29FF2583045734A /* EditorView+ScrollView.swift */,
				A31D7F0AD3CD39B734A09530C42A6005 /* EditorView+UIImage.swift */,
				9CC9CB4577D12D6D1A7DECEA072D9422 /* EditorView+UIView.swift */,
				543EABD5688C99ED261487FEA57C272C /* EditorViewProtocol.swift */,
				24B4FE0BE0C6D74C73084CD933B63758 /* ImageEditedResult.swift */,
				31B6F78E40C3969BB4BF56C347EAF9C7 /* VideoEditedResult.swift */,
			);
			name = EditorView;
			sourceTree = "<group>";
		};
		9DBEBFD39066BA1217F4ED3ACE187A33 /* Pods */ = {
			isa = PBXGroup;
			children = (
				3328FE722D4A1C7022B46881AFBE60F0 /* HXPhotoPicker */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		B4D06939873FD0776578A2D42BCA9062 /* Picker */ = {
			isa = PBXGroup;
			children = (
				B6A24E16A6DD4F0CFFEFD938B47114A7 /* SwiftAssetURLResult.swift */,
				492F778245EEAA2DD7580A4CA75F2D47 /* SwiftPhotoAsset.swift */,
				D9042614104217D39CBE9333FE1E93E7 /* SwiftPicker.swift */,
				8B8CF63B8795710CACC35F1BB469DD99 /* SwiftPickerConfiguration.swift */,
				626709B25F4E69020AC52FFEEF2B729A /* SwiftPickerResult.swift */,
			);
			name = Picker;
			path = JDTImageModule/Classes/Picker;
			sourceTree = "<group>";
		};
		BA4F31F07263C99FC76E66D632A59F09 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E52F34412E6BC30A55E934FC48826153 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C575145CD3DFBA05E4D87F845BC54A87 /* Resources */ = {
			isa = PBXGroup;
			children = (
				F7E2222F451C30E1968041519BD47E69 /* HXPhotoPicker.bundle */,
				662FC3274A5CFCC38963FEE272890857 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		C7EBB70BE09FE939BDEDFC6C1B055B20 /* Editor */ = {
			isa = PBXGroup;
			children = (
				C9EE28813A5C6684267B19D131168519 /* EditedResult.swift */,
				E8C67DC298675C064968142DBFC591EE /* Editor+CIImage.swift */,
				6F1EDAEE7459431DE287042C93B2FCD9 /* Editor+PhotoTools.swift */,
				2E87C5D5BA2D5A770503464702C68FE0 /* EditorAsset.swift */,
				D7EF51C12D0FA4B39CF1AAFE766D3731 /* EditorBrushBlockView.swift */,
				33E74DC8F2D7C01FC765E9EBD9359F60 /* EditorBrushColorView.swift */,
				C5B92DF75FC596DE9EB8FC8D2DE44D29 /* EditorBrushSizeView.swift */,
				8BFB97059FCFEA3D48BA32FE9D6A616C /* EditorChartlet.swift */,
				B35AC25D27FECE53721F41B14ADBEB6C /* EditorChartletListProtocol.swift */,
				C6DA0B4C54C3AB43E54B7F029910B3C2 /* EditorChartletPreviewView.swift */,
				5A486608DC3B74B458F3C4B6E599F6F8 /* EditorChartletViewCell.swift */,
				0088A37BB233E37D2F269B435A77AF93 /* EditorChartletViewController.swift */,
				9DCB0968DF5093644B0644294F4E27A9 /* EditorChartletViewListCell.swift */,
				14C0D4C7CA42AE7C18B6533B5C924568 /* EditorCollectionView.swift */,
				841297B6767DB799B3FE634A2860CCAB /* EditorConfiguration.swift */,
				38931FA28AE376649ACC359D39A0FE1A /* EditorFilterEditView.swift */,
				AE2DA113C179A1212BD24A28C3BE336F /* EditorFilterParameterView.swift */,
				81913EF7492B4077BB4E51D74E6D0320 /* EditorFiltersView.swift */,
				2BA272F2E202985D8D03EEEEBA93BC88 /* EditorMaskListProtocol.swift */,
				F88E3547CB6482B8A1478B48B67DC409 /* EditorMaskListViewCell.swift */,
				2A20059888303B51E7892486BC6B003A /* EditorMaskListViewController.swift */,
				23468F201EEFB6C1082900082A4B9A19 /* EditorMosaicToolView.swift */,
				1FBCD536381D27ED75B173451DAD9C44 /* EditorMusicListViewController.swift */,
				D1CB2DE05E19C0060AFC9A15A5DC3624 /* EditorMusicLyricViewCell.swift */,
				597F9F432888B2C069145B01FDB8FC41 /* EditorMusicView.swift */,
				CD92419874653D9402260A514AE16E6B /* EditorMusicViewCell.swift */,
				E84FC07A93C5FC648633FD090EFCA2B3 /* EditorPlayAuido.swift */,
				BE1069D2AE707CDAE357CAA9F6A39BFA /* EditorRatioToolView.swift */,
				558E84A3D3BB9CCDFA22EFE5D4472960 /* EditorRatioToolViewCell.swift */,
				1733B9CE45A9CC36C6FBFF922A0D3C8F /* EditorScaleView.swift */,
				DC32F67FE6674420C8095C93D2E7FE88 /* EditorStickerTextView.swift */,
				3BF6FFD36D652250E746F7300522BE42 /* EditorStickerTextView+CollectionView.swift */,
				640168DE94AFC74AA086F0F2C9D604EF /* EditorStickerTextView+Delegate.swift */,
				EC1A5AAA0EBE9051923DB55040755C65 /* EditorStickerTextView+Draw.swift */,
				70E207315CC6F2FE9AB3FD99F0B5D602 /* EditorStickerTextViewController.swift */,
				ED6704C7247DAD1DC24FF6D2E21B1CF6 /* EditorToolsView.swift */,
				11D34D65AFB3F2CD87764A64C97F93B7 /* EditorTransition.swift */,
				11A24B2CA6251FE86963A233AA476998 /* EditorType.swift */,
				22A6778713FB9291D095FC6C2CF15E5D /* EditorVideoControlMaskView.swift */,
				5AD5023CDB2089BBD3F7CEBB26C4B057 /* EditorVideoControlView.swift */,
				A1BF7C2A5D4AC66058E52AB8F224E69D /* EditorVideoControlViewCell.swift */,
				F3009C554D917A450D3FBA61DD02AC8C /* EditorViewController.swift */,
				BE803FCCDB346C8B5264CF06CA18C471 /* EditorViewController+Action.swift */,
				E6196CCBA9D405C4C3168A30537299D9 /* EditorViewController+Await.swift */,
				5B1250D07BCCD70BD33720C6E4FFE6B9 /* EditorViewController+Brush.swift */,
				B34EDEE6495B2C5E198EE324134DBAED /* EditorViewController+Chartlet.swift */,
				02F9DDF2E790E5CF5F5C31CCE85BA953 /* EditorViewController+EditorView.swift */,
				536BA9E2E17F41F53A01D6CB8C28DA14 /* EditorViewController+Filters.swift */,
				0346A114CDA8038E97C997FEAB11F808 /* EditorViewController+LoadAsset.swift */,
				D81301DEDDBA2299229CA5BE396FAF19 /* EditorViewController+Mosaic.swift */,
				EDE2BAED3C9DA439ED017DF8480D79B0 /* EditorViewController+Music.swift */,
				96A74C56499F32B1BA5EFD69F80379EB /* EditorViewController+Processing.swift */,
				BB2E9327281C7E6ADAAFA50519FBD70A /* EditorViewController+Ratio.swift */,
				7E23569E056ABA66F7622E1289D28A74 /* EditorViewController+Text.swift */,
				9D1ADF73EAB464C50E9A5AFEAF8493AE /* EditorViewController+ToolsView.swift */,
				50732B5E3676C444E0BEBA6275E6170A /* EditorViewController+UINavigationController.swift */,
				417AA4B3328803AC6CD258923E2716E7 /* EditorViewController+VideoControl.swift */,
				05C9AF4C66C4630CF97258ECB010F004 /* EditorViewControllerDelegate.swift */,
				E47C190F455C50D813B837ED03A6DB8F /* EditorVolumeView.swift */,
				8FD9805A29369B3863D67F309EDA5418 /* ExpandButton.swift */,
				3F214E118F12A7332004441BAE7DF6AE /* PhotoEditorFilter.swift */,
				7FF715A105B83DCE65C4A4B111DF8F1C /* VideoEditorMusic.swift */,
			);
			name = Editor;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				6E4EA2D3C98457F96E55D5E280079E48 /* Development Pods */,
				BA4F31F07263C99FC76E66D632A59F09 /* Frameworks */,
				9DBEBFD39066BA1217F4ED3ACE187A33 /* Pods */,
				6ED1B78986B855CE00C54F71D7D5595E /* Products */,
				4E019FB1F70649B146CEF7829FE54924 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D25ECB46F256B1F561BE4EAEA60DDB36 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				5CACAFC8C7EA8FF23B8984FD77077C8B /* HXPhotoPicker.modulemap */,
				201F43E6FB714D2F12BB4715611219AD /* HXPhotoPicker-dummy.m */,
				071D0BA493A9B71B143937D27E4E2D3B /* HXPhotoPicker-Info.plist */,
				5347F0C6F4372A43FFE10B4E243710A4 /* HXPhotoPicker-prefix.pch */,
				8926F9A654DB10F3FDFABCA3C68064A3 /* HXPhotoPicker-umbrella.h */,
				00646E2E29A24BF95CE89A4A1C731698 /* HXPhotoPicker.debug.xcconfig */,
				21584B0BEDD4E8887B70A3A2850EC6ED /* HXPhotoPicker.release.xcconfig */,
				5234AB30D6238048D373CE7864BF6AEB /* ResourceBundle-HXPhotoPicker_Privacy-HXPhotoPicker-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/HXPhotoPicker";
			sourceTree = "<group>";
		};
		D2C6E045ABF7EFED045FF97102C32BD8 /* Lite */ = {
			isa = PBXGroup;
			children = (
				A8A6A747A1356D5A504D90EC7A4A9C0D /* Camera+PhotoTools.swift */,
				449F6443E35251AD6C63B02F6B0439FE /* CameraBottomView.swift */,
				B286B37B58042187E66234C7AC79EB18 /* CameraConfiguration.swift */,
				89C068E6FDF9FF0EE81C869EF03FC1F0 /* CameraController.swift */,
				21203AADFE60A2EAA646679854B350D0 /* CameraControllerProtocol.swift */,
				AE1A52094CF232757E37725F426CEE59 /* CameraFilter.swift */,
				4405AE97A7290BF05BE6EBEDD921068B /* CameraManager.swift */,
				D8D93421E2B5D1BDD506143F05509416 /* CameraNormalPreviewView.swift */,
				E9F53129D099F4CD9F7E057C869CA57C /* CameraPreviewView.swift */,
				92D70113DE6E938C6208B194A4DF7FF1 /* CameraRenderer.swift */,
				A2D446A23DD905251DD8B14D443EE9D1 /* CameraResultViewController.swift */,
				C13D44321CF536452C258FA91D0591BB /* CameraViewController.swift */,
				3C4626E08C6D70CDA42B1716225D94F6 /* CameraViewController+BottomView.swift */,
				199A0EFAEAFB5F754B4F1DD54F46A303 /* CameraViewController+Editor.swift */,
				1C8FABE878D0FA2D18EAB57A981E8A0A /* CameraViewController+Preview.swift */,
				0348830034DF98880E51ACBB1694ED51 /* CameraViewController+Result.swift */,
				4BCC1E9AB6067A64C54EA5A0166CFB42 /* DeviceOrientationHelper.swift */,
				CB1CA96220C9601CD51E0FB92468894F /* PassThrough.metal */,
				75048AB2F7832942E5A02A116732FD0C /* PreviewMetalView.swift */,
			);
			name = Lite;
			sourceTree = "<group>";
		};
		DF07D376D1BCEE438B530F519AAFDC1A /* Location */ = {
			isa = PBXGroup;
			children = (
				6A9048CFCB871D7170D4EEB7A847CB0C /* CameraViewController+Location.swift */,
			);
			name = Location;
			sourceTree = "<group>";
		};
		E52F34412E6BC30A55E934FC48826153 /* iOS */ = {
			isa = PBXGroup;
			children = (
				CA0FA23181E1B0F518C7A51B4343E598 /* Foundation.framework */,
				6955F08988CC12E37BD1F81EEC8989F0 /* Photos.framework */,
				E4C4C22A0FB5ACB79DC6700191E54B09 /* PhotosUI.framework */,
				36A40A4132BAAA6ECA9020806AA973BF /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		F4CC24A86AC40B39DD680BAD058AE48B /* Support Files */ = {
			isa = PBXGroup;
			children = (
				901B9CB29917DA424A370715B31F2D18 /* JDTImageModule.modulemap */,
				B4EC2827755DE52F18E04B210B41E1C4 /* JDTImageModule-dummy.m */,
				82A9736DFF9D1A07390287D5FD1D3978 /* JDTImageModule-Info.plist */,
				D323956F59765864F203FA3F822BF6EE /* JDTImageModule-prefix.pch */,
				8D59106DE26A2FDE8E2B0BCFFBF2388F /* JDTImageModule-umbrella.h */,
				DBED3E327BC138821C2C07BE93160C03 /* JDTImageModule.debug.xcconfig */,
				C93160239E4BE981F01C4495C27DDA36 /* JDTImageModule.release.xcconfig */,
				CFD53186AF99D52B405D51C10468FC7C /* ResourceBundle-JDTImageModule-JDTImageModule-Info.plist */,
			);
			name = "Support Files";
			path = "../Pods/Target Support Files/JDTImageModule";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		3258CE9B5780FD82B2A09C3A641A7F64 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C744A3FAB547D00EDF836522E7CBBFE1 /* Pods-ObjCSwiftDemo-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F60404FEB7DCA14BCA600EE85BB98F4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				397F9B41BA46A7C2284772489141011D /* JDTImageModule.h in Headers */,
				D1A44D3825989315B3B6C888C9D420DC /* JDTImageModule-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C0A72C8043C0AC1FBF513E74E0386D2F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				71137B108952D67E23DDAD450AF495F7 /* HXPhotoPicker-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		229C8D8B66D775381ACB6E3EFE9D0354 /* JDTImageModule-JDTImageModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 64A51B4D4D392DA03349C5709C36320B /* Build configuration list for PBXNativeTarget "JDTImageModule-JDTImageModule" */;
			buildPhases = (
				8052E12EE9D4888FB84CE9B9C4343F76 /* Sources */,
				88F42E01B259355402E964BB8406B7AC /* Frameworks */,
				5FA4E482B106655000D723CCF9EE25B9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "JDTImageModule-JDTImageModule";
			productName = JDTImageModule;
			productReference = 881094C4979E410E6AF6E86318DA4F98 /* JDTImageModule.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		4A04CD391AA4051BA344312125D5AAD5 /* HXPhotoPicker-HXPhotoPicker_Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9823D7F8AF8B3DF797A7AF438D821333 /* Build configuration list for PBXNativeTarget "HXPhotoPicker-HXPhotoPicker_Privacy" */;
			buildPhases = (
				A2F50E18285DDD71A3A98DFE3376A987 /* Sources */,
				4BF81BA82339E5E3D243986FF48C1CD9 /* Frameworks */,
				8BD52EB5469538ECCC03E6F38135090B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "HXPhotoPicker-HXPhotoPicker_Privacy";
			productName = HXPhotoPicker_Privacy;
			productReference = 07FCAF745F3EEC27684AB03948F3A3EE /* HXPhotoPicker_Privacy.bundle */;
			productType = "com.apple.product-type.bundle";
		};
		4CAE7BADEF6DEA6F9871EE05A09D90E6 /* JDTImageModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7B451B2985A83584D280E6CB60A652ED /* Build configuration list for PBXNativeTarget "JDTImageModule" */;
			buildPhases = (
				9F60404FEB7DCA14BCA600EE85BB98F4 /* Headers */,
				CC189931C3B88540E6F39EED0FC87EF5 /* Sources */,
				C2165BBC3D03B20079526E3697299B0D /* Frameworks */,
				04A0D0115D5F12BD87E50E757735C09D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A84733FE991EFF3FA8B63D97F46DDD55 /* PBXTargetDependency */,
				125E0ED4AF7F5A67EF9C1FB2A664DDA3 /* PBXTargetDependency */,
			);
			name = JDTImageModule;
			productName = JDTImageModule;
			productReference = 3266178C3986BEA7E3DE52558B843164 /* JDTImageModule.framework */;
			productType = "com.apple.product-type.framework";
		};
		9F1801DEE1C1B558A27C0ECC86591198 /* Pods-ObjCSwiftDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 70FC08837A081B893AA9231D4E90DFF0 /* Build configuration list for PBXNativeTarget "Pods-ObjCSwiftDemo" */;
			buildPhases = (
				3258CE9B5780FD82B2A09C3A641A7F64 /* Headers */,
				681B78B402E7B1BF182CEE4377A2547B /* Sources */,
				534CA04219186E31829FF33A34555292 /* Frameworks */,
				4FB40A72B5D5A8C8197A86D78D913560 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A21D536809BF5BEE71667106BF8F020E /* PBXTargetDependency */,
				A620570BF5DAA23DAA461266BA0C7B80 /* PBXTargetDependency */,
			);
			name = "Pods-ObjCSwiftDemo";
			productName = Pods_ObjCSwiftDemo;
			productReference = DFB1FDE41AB9228BAB074037B242C24A /* Pods_ObjCSwiftDemo.framework */;
			productType = "com.apple.product-type.framework";
		};
		F8051AA643C524FA4E210DD0E6E62332 /* HXPhotoPicker */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CCE9D2511E6B8B83F17BBB2EEB6E1BA2 /* Build configuration list for PBXNativeTarget "HXPhotoPicker" */;
			buildPhases = (
				C0A72C8043C0AC1FBF513E74E0386D2F /* Headers */,
				F8869C9D65EB0E1A849A39E0777E46C4 /* Sources */,
				499162BE6A5970090821A5C213740A29 /* Frameworks */,
				2633E1EB787A275253654E6EAFB4C277 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C2B8DD4B20E08D32F3C150524D1BFDD3 /* PBXTargetDependency */,
			);
			name = HXPhotoPicker;
			productName = HXPhotoPicker;
			productReference = 24DDD4A0849B06C0942057220E1E81B2 /* HXPhotoPicker.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = 6ED1B78986B855CE00C54F71D7D5595E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F8051AA643C524FA4E210DD0E6E62332 /* HXPhotoPicker */,
				4A04CD391AA4051BA344312125D5AAD5 /* HXPhotoPicker-HXPhotoPicker_Privacy */,
				4CAE7BADEF6DEA6F9871EE05A09D90E6 /* JDTImageModule */,
				229C8D8B66D775381ACB6E3EFE9D0354 /* JDTImageModule-JDTImageModule */,
				9F1801DEE1C1B558A27C0ECC86591198 /* Pods-ObjCSwiftDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		04A0D0115D5F12BD87E50E757735C09D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D3C11D38AACB7DF788CEE332857D6F21 /* JDTImageModule.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2633E1EB787A275253654E6EAFB4C277 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8C535D2F470AF8228A5D50EC01D413B6 /* HXPhotoPicker.bundle in Resources */,
				7EF42324F1DD7EE0904DF7CD295FF05F /* HXPhotoPicker_Privacy.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4FB40A72B5D5A8C8197A86D78D913560 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5FA4E482B106655000D723CCF9EE25B9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8BD52EB5469538ECCC03E6F38135090B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				289706A79FF0D28513256167C16EDAC1 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		681B78B402E7B1BF182CEE4377A2547B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E17633D3EDE9ED20B106BE131844780B /* Pods-ObjCSwiftDemo-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8052E12EE9D4888FB84CE9B9C4343F76 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A2F50E18285DDD71A3A98DFE3376A987 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CC189931C3B88540E6F39EED0FC87EF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3FE5ECF5269F5056D39F1CF68106BA1B /* JDTImageModule.m in Sources */,
				E3E3360B3DF862514475FC1B03404A7B /* JDTImageModule-dummy.m in Sources */,
				5A483113B229CB6F8730B6CF705B431F /* SwiftAssetURLResult.swift in Sources */,
				5D3166A4D6E45B82FF281FF794425468 /* SwiftPhotoAsset.swift in Sources */,
				8762360834FA972528DAD859BEDF58D2 /* SwiftPicker.swift in Sources */,
				A2834EEB99A13624B55D293CA51B50EC /* SwiftPickerConfiguration.swift in Sources */,
				75D4882B00DE871F99A5A2EA3CC9D458 /* SwiftPickerResult.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8869C9D65EB0E1A849A39E0777E46C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D68AD3747130C9486C80692A7397B19 /* AlbumListConfiguration.swift in Sources */,
				BD1879EDC9A5214B2F3D00B0284B225A /* AlbumListView.swift in Sources */,
				98F97D26DCE55075E031FC3E98ADBDEF /* AlbumSectionHeaderView.swift in Sources */,
				79EE278DB89DD1E44F8E15116AF1D799 /* AlbumTitleView.swift in Sources */,
				713F3EE0490A0309288265BB22514DF6 /* AlbumTitleViewConfiguration.swift in Sources */,
				F5FA75788784A5FF88770412183337F0 /* AlbumViewBaseCell.swift in Sources */,
				3B156DCA4941F522609A11FB571004AA /* AlbumViewCell.swift in Sources */,
				6C69E7EAB0BE1C02B969022523213D56 /* AlbumViewController.swift in Sources */,
				E0587AC566F7B3CE7DE1B71FCD19CE41 /* AppearanceStyle.swift in Sources */,
				6A5F4AF035F97F95EF839DFCC898A9FD /* ArrowView.swift in Sources */,
				68167A2A22AB56DB0C252A46814626BF /* ArrowViewConfiguration.swift in Sources */,
				815FD6BF258CD19DE928ADDFFF039AE4 /* AssetError.swift in Sources */,
				E66978FD9F09E0DC0140ED2338C7E6F1 /* AssetManager.swift in Sources */,
				EA1842EBD935C133E39A7B031277DDBF /* AssetManager+Asset.swift in Sources */,
				3E977D12DEB787F907D57A658ACACD12 /* AssetManager+AVAsset.swift in Sources */,
				95302EB74A0B90067AD3B32143226CDD /* AssetManager+AVAssetExportSession.swift in Sources */,
				6A484BF0B4CAF05EBC4B6452302FF585 /* AssetManager+Image.swift in Sources */,
				C5001A1304C58E3BDEAA4F5DB5C319B0 /* AssetManager+ImageData.swift in Sources */,
				7EF24D26B2C456519A01415063D64A23 /* AssetManager+ImageURL.swift in Sources */,
				0F60986B410BEAD5BC32F0E48BB86B36 /* AssetManager+LivePhoto.swift in Sources */,
				141E3415C9CC3508B8741B4DD04D3541 /* AssetManager+LivePhotoURL.swift in Sources */,
				66B728964C6151E80727040119750ACD /* AssetManager+PlayerItem.swift in Sources */,
				DD506E173690C191669E9C6EF26B7B62 /* AssetManager+VideoURL.swift in Sources */,
				6BF9DD7A0234BB3A1B75E981C755591E /* AssetPermissionsUtil.swift in Sources */,
				7EB095945ADF589A965982B119274175 /* AssetResult.swift in Sources */,
				F658582934F37E43E877D8D96DDEE687 /* AssetSaveUtil.swift in Sources */,
				8530609D5058D8576CE816BA7BF2578B /* AssetURLResult.swift in Sources */,
				9BD9452635BB2E5A945DDC33D588F489 /* Camera+PhotoTools.swift in Sources */,
				8B92DFD720880B41BBD8170E9CACEE9B /* CameraBottomView.swift in Sources */,
				2AB2CEBF90DB5017FE4F73EA4000BED6 /* CameraConfiguration.swift in Sources */,
				3CEC6C4EB076539C4F236737F5F12DCF /* CameraController.swift in Sources */,
				8FE623BA0D23AF35529A5AE14400506A /* CameraControllerProtocol.swift in Sources */,
				772DACCBE150C3D241879CEB365D0C47 /* CameraFilter.swift in Sources */,
				BCFB0D4A14D2BB4EC4EB87F643035BB8 /* CameraManager.swift in Sources */,
				2EDF6C8155999174AADF520C998E5557 /* CameraNormalPreviewView.swift in Sources */,
				6654F00D2F23B297BC69C017AC92E5F6 /* CameraPreviewView.swift in Sources */,
				E643919B802A76207AFFE10C21ED1B13 /* CameraRenderer.swift in Sources */,
				2805F4E48D65F7E825E7269BCA32A162 /* CameraResultViewController.swift in Sources */,
				D7DD623490F2A9EB781D8877C7895888 /* CameraViewController.swift in Sources */,
				A394643D4B98317FD3074AEACEA3BC22 /* CameraViewController+BottomView.swift in Sources */,
				496F4BFE09CD95F1AECCAA55A361BD71 /* CameraViewController+Editor.swift in Sources */,
				DEEFBB403A2C3E2A4D39954392523996 /* CameraViewController+Location.swift in Sources */,
				059FDB4AFA187A2C2F35F27BFE6C0FED /* CameraViewController+Preview.swift in Sources */,
				878180707FEC090BD90927E633445223 /* CameraViewController+Result.swift in Sources */,
				4366B429396FCDAB3B6599D486D7DA9A /* CaptureVideoPreviewView.swift in Sources */,
				C55285E884784847935EEC6F764B17D6 /* Core+AVAsset.swift in Sources */,
				1CA7968C4102A3EE4F383AF962B976AF /* Core+Bundle.swift in Sources */,
				F56C0CBAC05DD479D08DB14EAA6F46C4 /* Core+CALayer.swift in Sources */,
				76DEE5DB7A6D0941766DB24D0ABFB43B /* Core+CGFloat.swift in Sources */,
				110256DA86C253C688595F0E23F82E64 /* Core+Data.swift in Sources */,
				C0FA470D66C93E671A1897EEBC5FEFA7 /* Core+Dictionary.swift in Sources */,
				DA885778DF3078434197D9F83333987C /* Core+FileManager.swift in Sources */,
				C4101AFE632FA075EE8C5D520873DA89 /* Core+LayerRTLFrame.swift in Sources */,
				9366D0507CA28C6693D3601FE868FFC7 /* Core+NSObject.swift in Sources */,
				D5E204519AF6AD02811202D4C93A8EDC /* Core+String.swift in Sources */,
				047B35E033F66EC193E901673F63B1FE /* Core+UIApplication.swift in Sources */,
				0DFFCE7A013D6C603BEACD43E79798F8 /* Core+UIBarButtonItem.swift in Sources */,
				C6BCB08865F4E8F3EDE42428A8E756D5 /* Core+UICollectionView.swift in Sources */,
				B7FA7A8220ADC24C96C3BAD3CF302722 /* Core+UIColor.swift in Sources */,
				9F8EA645A79528A3021D67BA136B1205 /* Core+UIDevice.swift in Sources */,
				D93429515CF44DEA2264CCF750463ACC /* Core+UIFont.swift in Sources */,
				D2B569F602F65E24158BF8AD615D70B0 /* Core+UIImage.swift in Sources */,
				A09385B03BA422F5D86EB388A1DFE001 /* Core+UIImageView.swift in Sources */,
				4B9F8EFB1C83153A069CE3C4870CFBFD /* Core+UILabel.swift in Sources */,
				7F233AC0C431B7E1F9053D6E44E2C7D0 /* Core+UITableView.swift in Sources */,
				DEB754BC853D84A4EE962D54960DF870 /* Core+UIView.swift in Sources */,
				B9C6F9D1EB6E3EDDDE1E2D33B5A8E76B /* Core+UIViewController.swift in Sources */,
				65F3D9527922CD3E97BFB7BA75F06186 /* Core+UIViewRTLFrame.swift in Sources */,
				EE8B0F1FB701065D5134B010DF577F1D /* Core+URL.swift in Sources */,
				739E93FF0CB3C6E9E21C9E20B10CE8BA /* CustomLanguage.swift in Sources */,
				7C82CAA3E92BD87D9FB6DF88D35E5002 /* DeniedAuthorizationView.swift in Sources */,
				AC035AEF5390BB1D2502B3AAB69790FE /* DeviceOrientationHelper.swift in Sources */,
				3621FBA32F1E8D96B380CA58A9574A75 /* EditedResult.swift in Sources */,
				59E30077498B82F5C00F7684B5F0495D /* Editor+CIImage.swift in Sources */,
				8EBF9511DC5E402FF1FDA0E4FB0E278B /* Editor+PhotoTools.swift in Sources */,
				9F72019EF49EFFE6684FDC487897852A /* EditorAdjusterView.swift in Sources */,
				698D6BDB7349E16A8B2809E1472DE9E5 /* EditorAdjusterView+ContentView.swift in Sources */,
				B50CFB59D5CA81D69694D847CBF20BAE /* EditorAdjusterView+Croper.swift in Sources */,
				E3DC636E1EE6A0B77B008DF8F0372EBA /* EditorAdjusterView+Edit.swift in Sources */,
				CF3106440DB6D5E0EBB0AB3DB6D5E495 /* EditorAdjusterView+FrameView.swift in Sources */,
				C6480A82634E148954A108AE3958F267 /* EditorAdjusterView+Mirror.swift in Sources */,
				2B1DB92F0277D2CBDB0708B7F53CC469 /* EditorAdjusterView+Rotate.swift in Sources */,
				0A4DF949FA08A578D4A5AC8D35E236F8 /* EditorAdjusterView+ScreenRotation.swift in Sources */,
				1704941945F8F75868500268E2D8E6E7 /* EditorAdjusterView+ScrollView.swift in Sources */,
				16C2F1099AD708B388D0A0092AA78866 /* EditorAdjusterView+Video.swift in Sources */,
				FC92FE6816B5C4DB5D3354EC95A6E372 /* EditorAdjusterViewProtocol.swift in Sources */,
				87732B623168DFA7D04D5312563C649E /* EditorAsset.swift in Sources */,
				05FE076907C9274A68280B31FB18E393 /* EditorAudioAnimationView.swift in Sources */,
				3963AC5F52AA88A234BF8294EDC17F6C /* EditorBrushBlockView.swift in Sources */,
				5B954DF2C1605BEC83A4823A5C8C1BDA /* EditorBrushColorView.swift in Sources */,
				ABC36F1C75B1173B2940517446388911 /* EditorBrushSizeView.swift in Sources */,
				12324F44E1A4D621BDFD40E1634E1E50 /* EditorCanvasView.swift in Sources */,
				6E21F39B0D6DF2E878419BE08D7FB120 /* EditorChartlet.swift in Sources */,
				9B33DD8A840AD16FA2741A857CD153CF /* EditorChartletListProtocol.swift in Sources */,
				6C495C5E8012A77E687698406E56F134 /* EditorChartletPreviewView.swift in Sources */,
				601AAD3564D19F2BB8DB4FE7D6D815E5 /* EditorChartletViewCell.swift in Sources */,
				CC4590878680CFFA690B6955A0A6D380 /* EditorChartletViewController.swift in Sources */,
				1D530B48E2747D03E4E4F4FAFCADCA11 /* EditorChartletViewListCell.swift in Sources */,
				7EEB8319D88149893F39AC3E264DC119 /* EditorCollectionView.swift in Sources */,
				344761B9B00FC1DAB0C9B096945D351C /* EditorConfiguration.swift in Sources */,
				063A899587A428B29E01AB14B05D8584 /* EditorContentView.swift in Sources */,
				0A7C7414326F7F7AF04F12D83722C035 /* EditorControlView.swift in Sources */,
				******************************** /* EditorDrawTool.swift in Sources */,
				7CB94059583173D991DBC559D1A24FED /* EditorDrawView.swift in Sources */,
				C0F9A1A3499589CC1450A7956FAAE673 /* EditorFilterEditView.swift in Sources */,
				2122D66069833F0A169100315B1C7CA5 /* EditorFilterParameterView.swift in Sources */,
				6EFA245A3A4DD7D19BDBA697BD1BF60B /* EditorFiltersView.swift in Sources */,
				A4D2B60779B2257B1A5A8A47FD45A1EA /* EditorFrameView.swift in Sources */,
				5C18B2BA1B17C1FFE6F01FD64DAB1E6A /* EditorFrameView+Control.swift in Sources */,
				065BEDFDD7BA4A8F9E2975E68D092418 /* EditorFrameView+VideoPlay.swift in Sources */,
				7303206966272F05F00B7E6BE032C4CC /* EditorMaskListProtocol.swift in Sources */,
				8F72EE52BB04220A8FE6D632F88F7235 /* EditorMaskListViewCell.swift in Sources */,
				94AE193C1BDCC2E3074AA1837474A977 /* EditorMaskListViewController.swift in Sources */,
				0789BB681C1946CE14BB8AE518B40C50 /* EditorMaskView.swift in Sources */,
				23A9B7FBD58B232AEE6727F456A8C572 /* EditorModels.swift in Sources */,
				609C0361B7025F4562BD774B6475FC0F /* EditorMosaicToolView.swift in Sources */,
				6C2AE2B3011CEB16B3FFD317756F6C1E /* EditorMosaicView.swift in Sources */,
				7D2A702D3B139ED2024B1D401C096182 /* EditorMusicListViewController.swift in Sources */,
				5B91752FF79445C23F7080A4AF0B7580 /* EditorMusicLyricViewCell.swift in Sources */,
				538FB8AEFCBA61936F28516F80E25A47 /* EditorMusicView.swift in Sources */,
				4F41F6F2A2C13625CAF61944D7E27403 /* EditorMusicViewCell.swift in Sources */,
				973C3A4B017B542E60025DEBDE7A2830 /* EditorPlayAuido.swift in Sources */,
				******************************** /* EditorRatioToolView.swift in Sources */,
				FE1F1D76EF579E5C02B7132CFD68C6A2 /* EditorRatioToolViewCell.swift in Sources */,
				EB8A3C65830F0EBFC1D85900B0502D4B /* EditorScaleView.swift in Sources */,
				C5ACDDD44CB1F33C6EF09BEF7AB8840B /* EditorStickersContentView.swift in Sources */,
				A7E3DBAEF90907EDC99CC73F57556259 /* EditorStickersItemView.swift in Sources */,
				CB9497B1D8C7B271C38D20B2139AB933 /* EditorStickersTrashView.swift in Sources */,
				0231505A0491BAF427A1E649B29FDB00 /* EditorStickersView.swift in Sources */,
				7CCA45FBE7E631E79B871CFF36DA7DBD /* EditorStickerTextView.swift in Sources */,
				A179A1208006CC2B578CA98F42B9CE34 /* EditorStickerTextView+CollectionView.swift in Sources */,
				2034AF4527E6E716F851F5DB6E3A3991 /* EditorStickerTextView+Delegate.swift in Sources */,
				6FBE6900FCAB9E38E002DE9327487B5B /* EditorStickerTextView+Draw.swift in Sources */,
				A0148A0989897F63A4DEDD6B4FCCF751 /* EditorStickerTextViewController.swift in Sources */,
				F99C4C0D48B240209FDF7370288C2A31 /* EditorToolsView.swift in Sources */,
				3E27495D0A1BBFB2F0807246C84E514B /* EditorTransition.swift in Sources */,
				B3FBC8C040A79E2761D011730D6F9262 /* EditorType.swift in Sources */,
				F961224500BF1B334159CB9536B4A220 /* EditorTypes.swift in Sources */,
				1D3E45E14940A615399DD9FD41D4D529 /* EditorVideoCompositor.swift in Sources */,
				B2C9D17C775C2F536D15E8F45D147202 /* EditorVideoControlMaskView.swift in Sources */,
				DDF9F3D8AE2EC839F87FD501EBFCB57D /* EditorVideoControlView.swift in Sources */,
				1F43DBCD2B0AFB949B27D20A613A22F2 /* EditorVideoControlViewCell.swift in Sources */,
				3DB735CB574F807AC51883F35A8D2CD2 /* EditorVideoPlayerView.swift in Sources */,
				2C4F88ECFA8CEC134CED2B2C8369D41F /* EditorVideoTool.swift in Sources */,
				3780A663CBB7545DBCD023AA09967BFE /* EditorView.swift in Sources */,
				1C6459974E86F169198C12D122CEC242 /* EditorView+AdjusterView.swift in Sources */,
				96693A9BC71203F814383B8B6879305E /* EditorView+AVAsset.swift in Sources */,
				F8F2CA31D1B68DE6E7E15FC87190BBD1 /* EditorView+CGFloat.swift in Sources */,
				F9F4C43AFD00FD41ACF786EFCD7A6ED9 /* EditorView+CIImage.swift in Sources */,
				607C8013C18BC7E814467B3167CA197B /* EditorView+GestureRecognizer.swift in Sources */,
				DA2E4FB83B640AE58D8138A6400BDF21 /* EditorView+PhotoTools.swift in Sources */,
				B70066F73A360DDF67B6D77C3F2A5871 /* EditorView+Public.swift in Sources */,
				FEB11D68761B46F6A3DF6B9C3A85D83C /* EditorView+ScrollView.swift in Sources */,
				283636146D8930C07D55E9AB91F6DAA2 /* EditorView+UIImage.swift in Sources */,
				6D9AD25D7865F25007CD87ED105D057B /* EditorView+UIView.swift in Sources */,
				2365DF6E1CA282F95099BAB1872D1B8D /* EditorViewController.swift in Sources */,
				049F02D95FC1D51F2FDC9BCC338C8426 /* EditorViewController+Action.swift in Sources */,
				9A1D31F601A3B9598D7CA682C4EFFEA1 /* EditorViewController+Await.swift in Sources */,
				E2CBDFEE6392CB9661667AABAA1CC0FC /* EditorViewController+Brush.swift in Sources */,
				4BE8F60A89FAF9C69E60590694DF942D /* EditorViewController+Chartlet.swift in Sources */,
				F2CDFD58D342C35A967C6DBA7F7F8353 /* EditorViewController+EditorView.swift in Sources */,
				B5C9079457DBC794B89E384BDE0580EC /* EditorViewController+Filters.swift in Sources */,
				67975D41C640F58BA1012A36D629A059 /* EditorViewController+LoadAsset.swift in Sources */,
				8E2A7471DF9CC846402188B365F366F5 /* EditorViewController+Mosaic.swift in Sources */,
				6A32A8A5890DA2C8C5BB987C55A6032A /* EditorViewController+Music.swift in Sources */,
				536E2BDAF86FA95C151391EE23339128 /* EditorViewController+Processing.swift in Sources */,
				CEA508576B639F39C839383E7640E569 /* EditorViewController+Ratio.swift in Sources */,
				D1A759D6A993F81CCE366E7D211F02D3 /* EditorViewController+Text.swift in Sources */,
				2245D941DEB1A4A701A78FB8981FEE08 /* EditorViewController+ToolsView.swift in Sources */,
				2BE86EA46DB846CA4366A3B4FB542F5E /* EditorViewController+UINavigationController.swift in Sources */,
				3F4D9DF8F22F09E3C07DDF9641918300 /* EditorViewController+VideoControl.swift in Sources */,
				7B279F06B581B451222AE55F4559860D /* EditorViewControllerDelegate.swift in Sources */,
				DE32DA670AC1FD7CA047F44CB61B2399 /* EditorViewProtocol.swift in Sources */,
				2550F732E25292A19EA01A8EFC2A8D79 /* EditorVolumeView.swift in Sources */,
				59A4446A67A63F0D4BCCAF8165A682A9 /* EmptyView.swift in Sources */,
				422AA2A05F85EDCC802432F28A97E747 /* EmptyViewConfiguration.swift in Sources */,
				62EC8C82566861FC10AE12330E439EAC /* ExpandButton.swift in Sources */,
				741EEFE77517A1C9C8E3F265EF63B646 /* ExportPreset.swift in Sources */,
				795D6732DA90FC01931D4906120EA9F6 /* HXBaseViewController.swift in Sources */,
				******************************** /* HXCollectionView.swift in Sources */,
				4D6BF38FD8738A7770DE8155BCF6761A /* HXImageViewProtocol.swift in Sources */,
				3BD3BFC1DABB62ADF0DAF49276E90B15 /* HXLog.swift in Sources */,
				2CB9277AEFB22070E81BA95350F513AE /* HXPhotoPicker.swift in Sources */,
				AE01DD1C5BC26F45EB977264E9942B22 /* HXPhotoPicker-dummy.m in Sources */,
				068D871E99CA49B8FE5F619592D4CAB4 /* HXTableView.swift in Sources */,
				75AB68A2022875F4634B3C1F1BB61BDB /* ImageContentType.swift in Sources */,
				EE95332D25230451A057BB12C9DE3594 /* ImageEditedResult.swift in Sources */,
				45941BBF42602BCDF4A88E51642AA2AD /* ImageResource.swift in Sources */,
				66BF424749D8826120E9131691CF23EE /* ImageViewConfig.swift in Sources */,
				ADF5C143C6BC2DEA4702A3B87C6938C1 /* IndicatorType.swift in Sources */,
				9DBE35CC02D5093C72CEE796F07D3048 /* LanguageType.swift in Sources */,
				EA0E43FD97F57A056C6B15704ACDD983 /* LivePhotoError.swift in Sources */,
				B934198CFA22CFFE128AFCAD9652F5D8 /* LocalAsset.swift in Sources */,
				674ED2701B9D30F36070A8DD6A16771F /* NetworkAsset.swift in Sources */,
				5C1D538E93467350635E61D01344DBA1 /* NotAuthorizedConfiguration.swift in Sources */,
				61E336411580D49811AAAC36717F621A /* PassThrough.metal in Sources */,
				57FD423C362F9E8EE0046CAA5567BB4A /* PhotoAlbumCollectionCell.swift in Sources */,
				F83382C6EE2405CF547E3BE0C9DDE26F /* PhotoAlbumCollectionViewCell.swift in Sources */,
				5DE0EB87D24196E93FD9E5A7941FFC68 /* PhotoAlbumController.swift in Sources */,
				8152B80FB17859F0368E348D19FE31B4 /* PhotoAlbumControllerConfiguration.swift in Sources */,
				6EFC2064B5FE6816B2AF4BBFCDDEF510 /* PhotoAlbumHeaderView.swift in Sources */,
				75335FF45AC4A35AFCF60958F1F4C371 /* PhotoAlbumList.swift in Sources */,
				E753679E39943D1BE0CE8A45D594760D /* PhotoAlbumViewCell.swift in Sources */,
				F808B701E06B76D7CE934FC246F53390 /* PhotoAlbumViewController.swift in Sources */,
				82902B4E01A6701B086DC92C40BA20C7 /* PhotoAsset.swift in Sources */,
				BA25B92A54CAB0C8D697DCD7C100BD84 /* PhotoAsset+Codable.swift in Sources */,
				4A3A86ECAAD9D8862571E4977C5D7E33 /* PhotoAsset+Editor.swift in Sources */,
				1C50B26888B74E66F5EB5567E16D2B7C /* PhotoAsset+Equatable.swift in Sources */,
				BF0D4E7C35C57FCE98B254DC01FA2B43 /* PhotoAsset+FileSize.swift in Sources */,
				31B86D9A156F7D44453138C1613566ED /* PhotoAsset+Image.swift in Sources */,
				0CA058366DAF34BC9974EA3F8809A3BB /* PhotoAsset+Local.swift in Sources */,
				BD827A4B5621011B6FC1B970BD7723D9 /* PhotoAsset+Network.swift in Sources */,
				B8FEB48433BA54DEF6B701723E39032A /* PhotoAsset+Request.swift in Sources */,
				A3B1967606AC546BDE90F6D69E723A28 /* PhotoAsset+URL.swift in Sources */,
				D2D07C1D3018405BA61AC66C61369B1A /* PhotoAsset+Video.swift in Sources */,
				2DE1AAABFEB9C73800A9037EAEB9F38B /* PhotoAssetCollection.swift in Sources */,
				7AF99AA905DC64B642549E80CC498739 /* PhotoBaseViewController.swift in Sources */,
				98AB7AED667FB8772D59524B7D66F86F /* PhotoBrowser.swift in Sources */,
				916FB428CB6D4FC61E4FDD12B50793E5 /* PhotoBrowserAnimationTransitioning.swift in Sources */,
				397FF29F31D42024FDD1D5C39497B139 /* PhotoBrowserAnimator.swift in Sources */,
				9109363DC5A5846B73F441A1303AD2B2 /* PhotoBrowserInteractiveAnimator.swift in Sources */,
				7B1753E366FB609817FB8ECCEF617FDD /* PhotoBrowserInteractiveTransition.swift in Sources */,
				C3CC1D806A559D613DE44F513F45F079 /* PhotoControllerEvent.swift in Sources */,
				B63ECAC7932BEA42C045C4BCF8C2CF74 /* PhotoDebugLogsConfig.swift in Sources */,
				ED7D28898742BFD89DAA7A11D0F2A31A /* PhotoDeniedAuthorization.swift in Sources */,
				069A5CCCBD3576373B5FC1DB69FAF8F4 /* PhotoEditorFilter.swift in Sources */,
				86D0F5B52165FDE0F9E831376CF459CD /* PhotoError.swift in Sources */,
				06F3282E415C4D0D4FAA687C191EE2F8 /* PhotoFetchAsset.swift in Sources */,
				534021B56AC29C7DCB7C2BB82D97375B /* PhotoFetchAssetCollection.swift in Sources */,
				8EA7150C933B758FE1C0DEA9F3C87431 /* PhotoFetchData.swift in Sources */,
				912F612A9FCA035C2A372402162D64F1 /* PhotoHUDConfig.swift in Sources */,
				FD188CF44C89C97E16B4A853C488893D /* PhotoHUDProtocol.swift in Sources */,
				0B70498CB7273343FA015D2D4572F186 /* PhotoImageCancelItemView.swift in Sources */,
				8F17189AFD9923BCBE477AF71C335FFE /* PhotoListCellConfiguration.swift in Sources */,
				8337943C7E955929BCAE38BB2C861CB5 /* PhotoListConfiguration.swift in Sources */,
				D8BBD6959FE880C14AF447F36F2445D2 /* PhotoLoadingView.swift in Sources */,
				10AD1FCCB522238346EB484407EA3889 /* PhotoManager.swift in Sources */,
				AFA710AB2E2E0542F6E9B1669F1CD9D1 /* PhotoManager+Download.swift in Sources */,
				A6418737B21B2F9BCEC1A319D56C3D3E /* PhotoManager+Language.swift in Sources */,
				7AE18A94ADD5D4872F2AF4EEF7526829 /* PhotoMyAlbumViewController.swift in Sources */,
				C343C5E885B0BE1F51F64212AF8BC8FB /* PhotoNavigationItem.swift in Sources */,
				4FE4BC84DF17E083C5A9CC86951CD5DA /* PhotoPanGestureRecognizer.swift in Sources */,
				4E701B90B03D0552F2D6E43CBBCCC91F /* PhotoPeekViewController.swift in Sources */,
				818DCD7706A9C325B478133DF046AEBF /* PhotoPermissionPromptView.swift in Sources */,
				2DFA94B4C6396B4323FF94B134618718 /* PhotoPickerBaseViewCell.swift in Sources */,
				E9AB38B55E5E2702C323A706C6599D45 /* PhotoPickerBottomNumberView.swift in Sources */,
				66F77F9B4A99261979F7C3FF76B7142B /* PhotoPickerController.swift in Sources */,
				F97B125BD2CE4546C771BB156D4D9EA5 /* PhotoPickerController+Internal.swift in Sources */,
				CB69E7D5F01FCAE91E8E068588318164 /* PhotoPickerController+PHPhotoLibrary.swift in Sources */,
				23286D3F937F555B1FEBBDD33559A2CC /* PhotoPickerController+PickerData.swift in Sources */,
				F56DB1B780C1236FBCE37C0E29AFFD82 /* PhotoPickerController+Transitioning.swift in Sources */,
				65D3359C78E19B5161412C44E5F9A388 /* PhotoPickerControllerAnimationTransitioning.swift in Sources */,
				5FF6A7544DC1DE8894DC82E21FD89BE8 /* PhotoPickerControllerAnimator.swift in Sources */,
				04E0FD8AFF834F9A368C23E1D6E2FC87 /* PhotoPickerControllerFectch.swift in Sources */,
				281C1E545CF9F25E05EBE1B017AF1EF8 /* PhotoPickerControllerInteractiveAnimator.swift in Sources */,
				A596001CD21970438E71DC6288238358 /* PhotoPickerControllerInteractiveTransition.swift in Sources */,
				586358E87CC311F45E2766E7DCD90F5D /* PhotoPickerControllerProtocol.swift in Sources */,
				7C58EE3B94159AD15F5DCAAAC22A1EF7 /* PhotoPickerData.swift in Sources */,
				F94A66C4EB7FC8F06905DDEBC16AA18A /* PhotoPickerDataStatus.swift in Sources */,
				EF2E0B3584AFDDB154B39CE862028859 /* PhotoPickerFilterItemView.swift in Sources */,
				4B558B0C51EF0BB543D0716AECA07F54 /* PhotoPickerFilterViewController.swift in Sources */,
				2D171A6E3C98F90C81A1561BD1506046 /* PhotoPickerFinishItemView.swift in Sources */,
				4BB92941B96CDC7846696FD49908D987 /* PhotoPickerLimitCell.swift in Sources */,
				D1078D485B3E3E4446AEF9C8560ED790 /* PhotoPickerList.swift in Sources */,
				48154D2EB68512DBC7F899F938D8CCD4 /* PhotoPickerListAssets.swift in Sources */,
				E92A724ED7CCE729F1FF19766994F8BA /* PhotoPickerListCollectionView.swift in Sources */,
				45EB5B45F50D1F8FE50A33C01FBB01C7 /* PhotoPickerListCondition.swift in Sources */,
				ED2CBCE10B26E8F8533DBB6C3F4B569A /* PhotoPickerListConfig.swift in Sources */,
				0D26A743870F898351BD096ABCB4FF9E /* PhotoPickerListFectchCell.swift in Sources */,
				7A15C180B422823246DA43464D8A026C /* PhotoPickerListSwipeSelect.swift in Sources */,
				CC06E4A67712CDA8949753FD9542C61C /* PhotoPickerListViewController.swift in Sources */,
				9EFE7DEE1E6D59C5B757941AFC4FCE9A /* PhotoPickerNavigationTitle.swift in Sources */,
				EFCC4F5512BA26F5F6A55A30D8C03D66 /* PhotoPickerPageViewController.swift in Sources */,
				1E675CF386023A02DF266265C9DBFD34 /* PhotoPickerSelectableViewCell.swift in Sources */,
				3F4BC18C2D815B06AB9BF39D38A35014 /* PhotoPickerSwitchLayout.swift in Sources */,
				B49257B71B0D87C2D05EDC5DD7F73AB4 /* PhotoPickerView.swift in Sources */,
				A80314EA5D7580B1D95049630AECDB9D /* PhotoPickerView+Asset.swift in Sources */,
				62ADB38DA0FAE0C4F68E96316AD5B63E /* PhotoPickerView+Camera.swift in Sources */,
				FB4749C83670D63D7F4EDFC6E2EC7FE2 /* PhotoPickerView+Cell.swift in Sources */,
				D7ABE4149788AD31C1D6642A4AFA059F /* PhotoPickerView+CollectionView.swift in Sources */,
				1A9753C7D97E0AEC64776015F275853E /* PhotoPickerView+Editor.swift in Sources */,
				FCB73B156077823F30123D8511E1D4C6 /* PhotoPickerView+Function.swift in Sources */,
				79E4140EE9CFD67AEE18CA10EC326271 /* PhotoPickerView+Preview.swift in Sources */,
				E3652AC8F91342221FF1E5F8A1D60ED3 /* PhotoPickerViewCell.swift in Sources */,
				8BC24DBBFBD4ECFB51FA93C6B2B5E887 /* PhotoPickerViewController.swift in Sources */,
				C0275FD4898DECB0AC51679735A4B36A /* PhotoPickerViewController+AlbumView.swift in Sources */,
				09A56C3A3ACC12E179623043504478B1 /* PhotoPickerViewController+Camera.swift in Sources */,
				E3819A9FF4F8A57BC5F58D0182377C43 /* PhotoPickerViewController+Editor.swift in Sources */,
				8F2520499806CE1DEAF252E4734EABC4 /* PhotoPickerViewController+FetchAsset.swift in Sources */,
				F855FE7853CFD6868FA6884BF3998680 /* PhotoPickerViewController+PhotoList.swift in Sources */,
				7196CC6EEA50D7AD1B3C7EF7105BCCBF /* PhotoPickerViewController+Preview.swift in Sources */,
				6B0729F954A4B8063805F72E60356AD7 /* PhotoPickerViewController+Toolbar.swift in Sources */,
				C0F5894C7DFB09DBFE7FF3EC59346BBC /* PhotoPickerViewProtocol.swift in Sources */,
				8DD69D59CE35E3F04C11E928B2DC4686 /* PhotoPickerWeChatViewCell.swift in Sources */,
				43B014FCC820E35F13024A718F724CCC /* PhotoPreviewContentLivePhotoView.swift in Sources */,
				0FAD3AA12D9FB2EA23100492C034847A /* PhotoPreviewContentPhotoView.swift in Sources */,
				80F04A9AB10462D7A2E3E24ED021BA73 /* PhotoPreviewContentVideoView.swift in Sources */,
				6A738CDB4AAB8C820F24E3BCCB944E65 /* PhotoPreviewContentViewProtocol.swift in Sources */,
				89850EC949F6A514447D0534D65800C2 /* PhotoPreviewListView.swift in Sources */,
				398B1B8540C146FA10C407E7C5D8627F /* PhotoPreviewListViewCell.swift in Sources */,
				ACD51E7F2A3E7D07D13252C0B462DFCE /* PhotoPreviewListViewLayout.swift in Sources */,
				0A39A1868886A5F4046984C4131F349E /* PhotoPreviewSelectedView.swift in Sources */,
				139108194D6874EBAAB1B69EEC1897BA /* PhotoPreviewSelectedViewCell.swift in Sources */,
				652CAA9CE102CD8EBFF1FFCD9B7DEAD8 /* PhotoPreviewVideoView.swift in Sources */,
				CF187822C94A59308FCE3124FA5F121A /* PhotoPreviewViewCell.swift in Sources */,
				4190DD3AB88E966137A04A9CDC08248F /* PhotoPreviewViewController.swift in Sources */,
				862BB21F4029B022C3CC26C5EED36F24 /* PhotoPreviewViewController+CollectionView.swift in Sources */,
				1CC5F19098A889DB68ACBADD7A8F5F46 /* PhotoPreviewViewController+Editor.swift in Sources */,
				2C7EEAB731F71F6EF6A1CC15C20DFC81 /* PhotoPreviewViewController+NavigationController.swift in Sources */,
				C76EA0805516EB24D9F19A7D5B35F3B4 /* PhotoPreviewViewController+SelectBox.swift in Sources */,
				******************************** /* PhotoPreviewViewController+Toolbar.swift in Sources */,
				3C718CA5D365F69C0B0B7C802AA4A1E3 /* PhotoPreviewViewControllerProtocol.swift in Sources */,
				9DD22BFB5831316C9FFD6BF1CEF04C12 /* PhotoSplitViewController.swift in Sources */,
				7AF22EC87DC307B49F83F4323933E2D8 /* PhotoTextCancelItemView.swift in Sources */,
				44CD38BC16ED32F10F4DFF5E85AD6EB1 /* PhotoThumbnailView.swift in Sources */,
				9A11D11FCD16DCD0A542B39BCC7BF759 /* PhotoToolBar.swift in Sources */,
				66A95237274807D361298EE5F2CFD8AC /* PhotoToolBarEmptyView.swift in Sources */,
				FA54A90AE492560C1FE96E5B7A02AD79 /* PhotoToolBarView.swift in Sources */,
				9C9606B61F983E79791542183A0D0EC1 /* PhotoTools.swift in Sources */,
				E9B7A969A4C550B149CD71F667CDF55E /* PhotoTools+Alert.swift in Sources */,
				6730B3E7B8B1B1E02E877815BB4415B5 /* PhotoTools+Camera.swift in Sources */,
				52379E08BD62C7842F89C1969FDF4232 /* PhotoTools+File.swift in Sources */,
				B3FC87F1679B685740D7CE20080E7627 /* Picker+Array.swift in Sources */,
				EBE3302A8962007DA08E11A204F929FF /* Picker+ConfigExtension.swift in Sources */,
				268846FCA0CE6314BCF55623DAAEABFA /* Picker+Int.swift in Sources */,
				8C87884A3F3A000C591C402143BAE4CA /* Picker+LivePhotoTools.swift in Sources */,
				343163896BF7A76E77B00C67E35FE031 /* Picker+PHAsset.swift in Sources */,
				6DAD89AC5A6C6A43BC1AA99872684CA5 /* Picker+PHAssetCollection.swift in Sources */,
				3A96CDCE1022A23415946DEA63D012AD /* Picker+PhotoAsset.swift in Sources */,
				CDAAFF4B64D115A72C11D0840BE5B404 /* Picker+PhotoManager.swift in Sources */,
				13710A5FB4AF056762112E19815E6FA7 /* Picker+PhotoTools.swift in Sources */,
				F58737A0040FBD8B44344F23444F3051 /* Picker+UIImageView.swift in Sources */,
				E9A33DB21AF9F47CEFB7B2127E940A3E /* Picker+UIViewController.swift in Sources */,
				99E438474C1A294A21BE3D736FDBC301 /* PickerBottomViewConfiguration.swift in Sources */,
				7F517F94FB65A9E0D3A6603F9F6287F3 /* PickerCameraViewCell.swift in Sources */,
				5ACB3C49D7AF640ADDEC4AE1539F62E0 /* PickerConfiguration.swift in Sources */,
				2FE7B8CDF43FB456370B78D9ED8AF950 /* PickerInteractiveTransition.swift in Sources */,
				1EA558734E0FF885FE9E316B2BE97608 /* PickerManager.swift in Sources */,
				0294727FA92458294940E5A4E19AF8A8 /* PickerResult.swift in Sources */,
				E8C177370E7DBBE801F10F7502A1B723 /* PickerTransition.swift in Sources */,
				E124A926E123F29808AFFB174137BEA4 /* PickerTypes.swift in Sources */,
				8149FCCEE03447DCBD111C9077F9E5A1 /* PlayButton.swift in Sources */,
				2399E5BF2C7D8AD4ACF1AF9402532086 /* PreviewLivePhotoViewCell.swift in Sources */,
				6DEA6664BD227E4E53FD2AC0F3E05847 /* PreviewMetalView.swift in Sources */,
				CD4DAC4E58C079C0A9D7F64E6B17CAF5 /* PreviewPhotoViewCell.swift in Sources */,
				CD193461C847ADFDC3D79CEE1189D218 /* PreviewVideoControlViewCell.swift in Sources */,
				D41BDBD73DB5EEA0D597C8333861F766 /* PreviewVideoViewCell.swift in Sources */,
				C0AD4C8F8A76E446038D94B6841579A2 /* PreviewViewConfiguration.swift in Sources */,
				F2698785BEA19B505A5453CE03449EC0 /* ProgressCircleView.swift in Sources */,
				8FE081F4602A15EF20A2C92015A41D6B /* ProgressCricleJoinView.swift in Sources */,
				D1A47923A25AB21A2B9379CF85862A92 /* ProgressHUD.swift in Sources */,
				70A081593BBA97C77CD291D6DF4ECED7 /* ProgressImageView.swift in Sources */,
				AAEBB06DA59DD58633B0A7BFACF9615A /* ProgressIndefiniteView.swift in Sources */,
				A70BF0131CC661B468DA3E2CA8FFAAA6 /* SelectBoxConfiguration.swift in Sources */,
				******************************** /* SelectBoxView.swift in Sources */,
				******************************** /* SliderView.swift in Sources */,
				C1E60A83D3C0668FBA5518FC4107A63C /* SystemCameraConfiguration.swift in Sources */,
				CA6938E564C96FAD052B3750A5158E66 /* SystemCameraViewController.swift in Sources */,
				31CB4A2657B8DDB27681998914E06F15 /* TextManager.swift in Sources */,
				11EC7C878EEA96C6D2E7EC848106413B /* TickView.swift in Sources */,
				B2BEDEC2D83ED7224E94CFC4DCA37F95 /* VideoEditedResult.swift in Sources */,
				069D4B78EAD1F49FD8699D423A312885 /* VideoEditorMusic.swift in Sources */,
				63A4E2D170F19839F4FBA790C579AFB4 /* VideoPlayerView.swift in Sources */,
				92CE1FF5026522FA76C89192892EBE85 /* VideoPlaySliderView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		125E0ED4AF7F5A67EF9C1FB2A664DDA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "JDTImageModule-JDTImageModule";
			target = 229C8D8B66D775381ACB6E3EFE9D0354 /* JDTImageModule-JDTImageModule */;
			targetProxy = FE3F564F64ECBB4E5D559296438FD67C /* PBXContainerItemProxy */;
		};
		A21D536809BF5BEE71667106BF8F020E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = HXPhotoPicker;
			target = F8051AA643C524FA4E210DD0E6E62332 /* HXPhotoPicker */;
			targetProxy = AE19D09E742F40D77AD90288A608C0DB /* PBXContainerItemProxy */;
		};
		A620570BF5DAA23DAA461266BA0C7B80 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JDTImageModule;
			target = 4CAE7BADEF6DEA6F9871EE05A09D90E6 /* JDTImageModule */;
			targetProxy = 91EAFA8DC81FF570C580B141D9B5E7CB /* PBXContainerItemProxy */;
		};
		A84733FE991EFF3FA8B63D97F46DDD55 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = HXPhotoPicker;
			target = F8051AA643C524FA4E210DD0E6E62332 /* HXPhotoPicker */;
			targetProxy = 93BC09FAE3869F52D063564CA6D5FAD3 /* PBXContainerItemProxy */;
		};
		C2B8DD4B20E08D32F3C150524D1BFDD3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "HXPhotoPicker-HXPhotoPicker_Privacy";
			target = 4A04CD391AA4051BA344312125D5AAD5 /* HXPhotoPicker-HXPhotoPicker_Privacy */;
			targetProxy = 85BDEF53F401F1CC3813C0D4E070085C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1CE5B0D3E3B6D56A3ACCCA6777A96D48 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 00646E2E29A24BF95CE89A4A1C731698 /* HXPhotoPicker.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/HXPhotoPicker";
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IBSC_MODULE = HXPhotoPicker;
				INFOPLIST_FILE = "Target Support Files/HXPhotoPicker/ResourceBundle-HXPhotoPicker_Privacy-HXPhotoPicker-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				PRODUCT_NAME = HXPhotoPicker_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		22216DDCF08CB265FF370CDC11BA8E06 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 21584B0BEDD4E8887B70A3A2850EC6ED /* HXPhotoPicker.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/HXPhotoPicker";
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IBSC_MODULE = HXPhotoPicker;
				INFOPLIST_FILE = "Target Support Files/HXPhotoPicker/ResourceBundle-HXPhotoPicker_Privacy-HXPhotoPicker-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				PRODUCT_NAME = HXPhotoPicker_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		2AC8AA4B84B0A78E86DF7C137E7BC438 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C93160239E4BE981F01C4495C27DDA36 /* JDTImageModule.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/JDTImageModule/JDTImageModule-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/JDTImageModule/JDTImageModule-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/JDTImageModule/JDTImageModule.modulemap";
				PRODUCT_MODULE_NAME = JDTImageModule;
				PRODUCT_NAME = JDTImageModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		34954E01B22253085C4602889B6F2C84 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DBED3E327BC138821C2C07BE93160C03 /* JDTImageModule.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/JDTImageModule";
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IBSC_MODULE = JDTImageModule;
				INFOPLIST_FILE = "Target Support Files/JDTImageModule/ResourceBundle-JDTImageModule-JDTImageModule-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = JDTImageModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		6ECD6ACEBEAC890AE2D41DE3AA3BB0DE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6151F9B26EA26FD1DAF8AE03B74B9D97 /* Pods-ObjCSwiftDemo.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-ObjCSwiftDemo/Pods-ObjCSwiftDemo-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ObjCSwiftDemo/Pods-ObjCSwiftDemo.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		83E279C81DCDC278DA66FEF02D7C6115 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 00646E2E29A24BF95CE89A4A1C731698 /* HXPhotoPicker.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/HXPhotoPicker/HXPhotoPicker-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/HXPhotoPicker/HXPhotoPicker-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/HXPhotoPicker/HXPhotoPicker.modulemap";
				PRODUCT_MODULE_NAME = HXPhotoPicker;
				PRODUCT_NAME = HXPhotoPicker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A1E7DB0D1EC4930A5675578B41E47B02 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C93160239E4BE981F01C4495C27DDA36 /* JDTImageModule.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/JDTImageModule";
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IBSC_MODULE = JDTImageModule;
				INFOPLIST_FILE = "Target Support Files/JDTImageModule/ResourceBundle-JDTImageModule-JDTImageModule-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = JDTImageModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		A3CA710B05DD0D38FF0C61760F3B99FB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		CEA4B6CAF125D9D33FC65ED4B59050B2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F6DC3B5FBAA2F684B64BB7B512B57442 /* Pods-ObjCSwiftDemo.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-ObjCSwiftDemo/Pods-ObjCSwiftDemo-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ObjCSwiftDemo/Pods-ObjCSwiftDemo.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		E2364721D6682F8B9E80045232309A09 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		E40C2E67743698066A021E97C5DC4CF0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DBED3E327BC138821C2C07BE93160C03 /* JDTImageModule.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/JDTImageModule/JDTImageModule-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/JDTImageModule/JDTImageModule-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/JDTImageModule/JDTImageModule.modulemap";
				PRODUCT_MODULE_NAME = JDTImageModule;
				PRODUCT_NAME = JDTImageModule;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		FA2C706522FD613CDB4717F8EFC6392E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 21584B0BEDD4E8887B70A3A2850EC6ED /* HXPhotoPicker.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/HXPhotoPicker/HXPhotoPicker-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/HXPhotoPicker/HXPhotoPicker-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/HXPhotoPicker/HXPhotoPicker.modulemap";
				PRODUCT_MODULE_NAME = HXPhotoPicker;
				PRODUCT_NAME = HXPhotoPicker;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A3CA710B05DD0D38FF0C61760F3B99FB /* Debug */,
				E2364721D6682F8B9E80045232309A09 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		64A51B4D4D392DA03349C5709C36320B /* Build configuration list for PBXNativeTarget "JDTImageModule-JDTImageModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				34954E01B22253085C4602889B6F2C84 /* Debug */,
				A1E7DB0D1EC4930A5675578B41E47B02 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		70FC08837A081B893AA9231D4E90DFF0 /* Build configuration list for PBXNativeTarget "Pods-ObjCSwiftDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6ECD6ACEBEAC890AE2D41DE3AA3BB0DE /* Debug */,
				CEA4B6CAF125D9D33FC65ED4B59050B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7B451B2985A83584D280E6CB60A652ED /* Build configuration list for PBXNativeTarget "JDTImageModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E40C2E67743698066A021E97C5DC4CF0 /* Debug */,
				2AC8AA4B84B0A78E86DF7C137E7BC438 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9823D7F8AF8B3DF797A7AF438D821333 /* Build configuration list for PBXNativeTarget "HXPhotoPicker-HXPhotoPicker_Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1CE5B0D3E3B6D56A3ACCCA6777A96D48 /* Debug */,
				22216DDCF08CB265FF370CDC11BA8E06 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CCE9D2511E6B8B83F17BBB2EEB6E1BA2 /* Build configuration list for PBXNativeTarget "HXPhotoPicker" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83E279C81DCDC278DA66FEF02D7C6115 /* Debug */,
				FA2C706522FD613CDB4717F8EFC6392E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
