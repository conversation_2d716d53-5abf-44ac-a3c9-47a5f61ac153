//
//  SwiftTest.swift
//  JDTImageModule
//
//  Created by lvchenzhu.1 on 2025/9/15.
//

import Foundation
import UIKit

@objc(SwiftTest)
public class SwiftTest: NSObject {
    @objc public var name: String = "SwiftTest"

    @objc public func getName() -> String {
        NSLog("SwiftTest getName called")

        // 测试调用 Objective-C 代码
        let objcModule = JDTImageModule()
        objcModule.test()

        return name
    }

    @objc public override init() {
        super.init()
    }
}
