# JDTImageModule

[![CI Status](https://img.shields.io/travis/lvchenzhu.1/JDTImageModule.svg?style=flat)](https://travis-ci.org/lvchenzhu.1/JDTImageModule)
[![Version](https://img.shields.io/cocoapods/v/JDTImageModule.svg?style=flat)](https://cocoapods.org/pods/JDTImageModule)
[![License](https://img.shields.io/cocoapods/l/JDTImageModule.svg?style=flat)](https://cocoapods.org/pods/JDTImageModule)
[![Platform](https://img.shields.io/cocoapods/p/JDTImageModule.svg?style=flat)](https://cocoapods.org/pods/JDTImageModule)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

JDTImageModule is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'JDTImageModule'
```

## Author

lvchenzhu.1, <EMAIL>

## License

JDTImageModule is available under the MIT license. See the LICENSE file for more info.
